﻿#include "sub_rule_manager.h"
#include "tcp_client.h"

SubRuleManager& SubRuleManager::getInstance()
{
    static SubRuleManager instance;
    return instance;
}

SubRuleManager::SubRuleManager()
{
    qDebug() << "SubRuleManager initialized";
}

SubRuleManager::~SubRuleManager()
{
    QMutexLocker locker(&m_mutex);
    m_benchRules.clear();
    m_registeredWidgets.clear();
    m_widgetRules.clear();
    qDebug() << "SubRuleManager destroyed";
}

void SubRuleManager::addRules(void* widgetId, int benchId, const QList<SubscribeRule>& rules)
{
    QMutexLocker locker(&m_mutex);
    
    if (rules.isEmpty()) {
        qDebug() << "No rules to add for widget" << widgetId << "bench" << benchId;
        return;
    }
    
    QList<SubscribeRule> addedRules;
    
    // 更新规则引用计数
    for (const auto& rule : rules) {
        auto& benchRules = m_benchRules[benchId];
        auto& ruleRef = benchRules[rule];
        
        if (ruleRef.refCount == 0) {
            // 新规则，需要发送订阅
            ruleRef.rule = rule;
            addedRules.append(rule);
        }
        
        ruleRef.refCount++;
        ruleRef.widgets.insert(widgetId);
        
        // 记录界面的规则
        m_widgetRules[widgetId][benchId].append(rule);
    }
    
    qDebug() << "Added" << rules.size() << "rules for widget" << widgetId 
             << "bench" << benchId << ", new rules:" << addedRules.size();
    
    // 发送订阅更新
    if (!addedRules.isEmpty()) {
        sendSubscriptionUpdate(benchId, addedRules, QList<SubscribeRule>());
        emit rulesChanged(benchId, addedRules, QList<SubscribeRule>());
    }
}

void SubRuleManager::delRules(void* widgetId, int benchId, const QList<SubscribeRule>& rules)
{
    QMutexLocker locker(&m_mutex);
    
    if (rules.isEmpty()) {
        qDebug() << "No rules to delete for widget" << widgetId << "bench" << benchId;
        return;
    }
    
    QList<SubscribeRule> removedRules;
    
    // 更新规则引用计数
    for (const auto& rule : rules) {
        auto benchIt = m_benchRules.find(benchId);
        if (benchIt == m_benchRules.end()) {
            continue;
        }
        
        auto ruleIt = benchIt->find(rule);
        if (ruleIt == benchIt->end()) {
            continue;
        }
        
        auto& ruleRef = ruleIt.value();
        ruleRef.widgets.remove(widgetId);
        ruleRef.refCount--;
        
        if (ruleRef.refCount <= 0) {
            // 没有界面引用该规则，需要取消订阅
            removedRules.append(rule);
            benchIt->erase(ruleIt);
        }
        
        // 从界面规则记录中移除
        auto widgetIt = m_widgetRules.find(widgetId);
        if (widgetIt != m_widgetRules.end()) {
            auto benchRulesIt = widgetIt->find(benchId);
            if (benchRulesIt != widgetIt->end()) {
                benchRulesIt->removeAll(rule);
                if (benchRulesIt->isEmpty()) {
                    widgetIt->erase(benchRulesIt);
                }
            }
        }
    }
    
    qDebug() << "Deleted" << rules.size() << "rules for widget" << widgetId 
             << "bench" << benchId << ", removed rules:" << removedRules.size();
    
    // 发送取消订阅更新
    if (!removedRules.isEmpty()) {
        sendSubscriptionUpdate(benchId, QList<SubscribeRule>(), removedRules);
        emit rulesChanged(benchId, QList<SubscribeRule>(), removedRules);
    }
}

void SubRuleManager::updateRules(void* widgetId, int benchId, 
                                 const QList<SubscribeRule>& oldRules, 
                                 const QList<SubscribeRule>& newRules)
{
    QMutexLocker locker(&m_mutex);
    
    // 先删除旧规则
    if (!oldRules.isEmpty()) {
        locker.unlock();
        delRules(widgetId, benchId, oldRules);
        locker.relock();
    }
    
    // 再添加新规则
    if (!newRules.isEmpty()) {
        locker.unlock();
        addRules(widgetId, benchId, newRules);
        locker.relock();
    }
    
    qDebug() << "Updated rules for widget" << widgetId << "bench" << benchId
             << "old:" << oldRules.size() << "new:" << newRules.size();
}

void SubRuleManager::clearWidgetRules(void* widgetId)
{
    QMutexLocker locker(&m_mutex);
    
    auto widgetIt = m_widgetRules.find(widgetId);
    if (widgetIt == m_widgetRules.end()) {
        return;
    }
    
    // 删除该界面的所有规则
    for (auto benchIt = widgetIt->begin(); benchIt != widgetIt->end(); ++benchIt) {
        int benchId = benchIt.key();
        const auto& rules = benchIt.value();
        locker.unlock();
        delRules(widgetId, benchId, rules);
        locker.relock();
    }
    
    // 清除界面记录
    m_widgetRules.erase(widgetIt);
    
    qDebug() << "Cleared all rules for widget" << widgetId;
}

QList<SubRuleManager::SubscribeRule> SubRuleManager::getCurrentRules(int benchId) const
{
    QMutexLocker locker(&m_mutex);
    
    QList<SubscribeRule> rules;
    auto benchIt = m_benchRules.find(benchId);
    if (benchIt != m_benchRules.end()) {
        for (auto it = benchIt->begin(); it != benchIt->end(); ++it) {
            if (it->refCount > 0) {
                rules.append(it->rule);
            }
        }
    }
    
    return rules;
}

void SubRuleManager::registerWidget(void* widgetId, QObject* widget)
{
    QMutexLocker locker(&m_mutex);
    m_registeredWidgets[widgetId] = widget;
    qDebug() << "Registered widget" << widgetId << widget;
}

void SubRuleManager::unregisterWidget(void* widgetId)
{
    QMutexLocker locker(&m_mutex);
    
    // 清除该界面的所有订阅规则
    locker.unlock();
    clearWidgetRules(widgetId);
    locker.relock();
    
    // 移除注册记录
    m_registeredWidgets.remove(widgetId);
    
    qDebug() << "Unregistered widget" << widgetId;
}

void SubRuleManager::sendSubscriptionUpdate(int benchId, const QList<SubscribeRule>& addedRules, 
                                           const QList<SubscribeRule>& removedRules)
{
    TcpClient& client = TcpClient::getInstance();
    
    // 发送新增订阅
    if (!addedRules.isEmpty()) {
        SUBSCRIBE_PACKAGE_CMD_SUB subPackage = {};
        subPackage.header.command = CMD_SUBSCRIBE;
        
        for (const auto& rule : addedRules) {
            subPackage.subRuleVec.push_back(toTcpRule(rule));
        }
        
        subPackage.header.packLength = sizeof(SUBSCRIBE_PACKAGE_CMD_SUB) + 
                                      addedRules.size() * sizeof(SUBSCRIBE_PACKAGE_SUB_RULE);
        
        // 序列化并发送
        char buffer[BUFFER_SIZE] = {0};
        int len = serialize(&subPackage, sizeof(subPackage), buffer);
        memcpy(buffer, &len, sizeof(len));
        QByteArray data(buffer, len);
        
        bool success = client.sendData(benchId, data);
        qDebug() << "Sent subscribe rules for bench" << benchId << "count:" << addedRules.size() 
                 << "success:" << success;
    }
    
    // 发送取消订阅
    if (!removedRules.isEmpty()) {
        SUBSCRIBE_PACKAGE_CMD_UNSUB unsubPackage = {};
        unsubPackage.header.command = CMD_UNSUBSCRIBE;
        
        for (const auto& rule : removedRules) {
            unsubPackage.unSubRUleVec.push_back(toTcpRule(rule));
        }
        
        unsubPackage.header.packLength = sizeof(SUBSCRIBE_PACKAGE_CMD_UNSUB) + 
                                        removedRules.size() * sizeof(SUBSCRIBE_PACKAGE_SUB_RULE);
        
        // 序列化并发送
        char buffer[BUFFER_SIZE] = {0};
        int len = serialize(&unsubPackage, sizeof(unsubPackage), buffer);
        memcpy(buffer, &len, sizeof(len));
        QByteArray data(buffer, len);
        
        bool success = client.sendData(benchId, data);
        qDebug() << "Sent unsubscribe rules for bench" << benchId << "count:" << removedRules.size() 
                 << "success:" << success;
    }
}

SUBSCRIBE_PACKAGE_SUB_RULE SubRuleManager::toTcpRule(const SubscribeRule& rule) const
{
    SUBSCRIBE_PACKAGE_SUB_RULE tcpRule;
    tcpRule.sourceId = rule.sourceId;
    tcpRule.topicId = rule.topicId;
    return tcpRule;
}
