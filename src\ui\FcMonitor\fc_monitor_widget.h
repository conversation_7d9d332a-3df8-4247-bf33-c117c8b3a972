﻿#ifndef FC_MONITOR_WIDGET_H
#define FC_MONITOR_WIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QTreeView>
#include <QStandardItemModel>
#include <QListWidget>
#include <QTabWidget>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QLineEdit>
#include <QTimer>
#include <QMap>
#include <QSet>
#include <QMessageBox>

#include "fc_data_manager.h"
#include "fc_data_parser.h"
#include "../scheme/scheme_add_dialog.h"
#include "../scheme/history_data_store.h"
#include "../scheme/template_list_item.h"
#include "../scheme/template_list_widget.h"
#include "tcpFrameStruct.h"
#include "unit_combo_delegate.h"
#include "../../utils/sub_rule_manager.h"

/**
 * @brief FC监控界面
 * 参考SchemeManagement的界面布局和功能，实现FC数据的两层级树形展示
 * 第一层级：功能单元消息
 * 第二层级：主题
 */

namespace Ui {
class FcMonitorWidget;
}
class FcMonitorWidget : public QWidget
{
    Q_OBJECT

public:
    explicit FcMonitorWidget(QWidget *parent = nullptr);
    ~FcMonitorWidget();

    // 重新解析本地方案文件并更新方案列表
    void loadScheme();
    void addScheme(const FcSchemeData& scheme);
    void removeScheme(const QString& schemeName);
    QStringList getSchemeNames() const;

    // 监控控制
    void startMonitoring(const QString& schemeName);
    void stopMonitoring(const QString& schemeName);
    void pauseMonitoring();
    void resumeMonitoring();
    bool isMonitoring() const;
    bool isPaused() const;

    // 数据更新
    void updateData(const QByteArray& rawData);
    void updateMessageData(const QString& schemeName, const QString& messageId,
                           const QMap<QString, QString>& topicValues);

signals:
    void schemeSelected(const QString& schemeName);
    void messageClicked(const QString& schemeName, const QString& messageId);
    void topicClicked(const QString& schemeName, const QString& messageId, const QString& topicId);
    void monitoringStateChanged(bool isMonitoring);
    void dataUpdated(const QString& schemeName);
    void addSchemefinishedSignal(int ret);

private slots:
    void onSchemeItemClicked(QListWidgetItem* item);
    void onTreeItemClicked(const QModelIndex& index);
    void onTabChanged(int index);
    void onSchemeAndTemplateTabChanged(int index);
    void onSearchTextChanged(const QString& text);
    void onAddSchemeClicked();
    void onStartMonitoringClicked();
    void onStopMonitoringClicked();
    void onPauseClicked();
    void onClearDataClicked();
    //递归函数清除所有叶子节点的第1列和第8列数据
    void clearLeafNodeData(QStandardItem *parentItem);
    void onExportDataClicked();
    void onUpdateTimer();

    //方案页关闭槽函数
    void onDataTabClosed(int index);

    // 数据管理器信号处理
    void onSchemeDataChanged(const QString& schemeName);
    void onSchemeAdded(const QString& schemeName);
    void onMessageDataUpdated(const QString& schemeName, const QString& messageId);
    void onTopicDataUpdated(const QString& schemeName, const QString& messageId, const QString& topicId);
    void onParseError(const QString& errorMessage);

    // 添加方案结束
    void addSchemefinished(int ret);
    void editSchemefinished(int ret);

    //时间列表点击槽函数
    void TimeListWidgetRowChanged(int index);


    void onTemplateDeleted(const QString& templateName);
    void onTemplateDownload();

    void on_settingsButton_currentIndexChanged(int index);

    // 单位切换槽函数
    void onUnitChanged(const QModelIndex &index, const QString &oldUnit, const QString &newUnit,
                      unsigned int oldUnitID, unsigned int newUnitID);
    // 展开、收缩功能点击
    void onLayoutButtonClicked();
    
    // 显示/隐藏帧头按钮点击
    void onShowHeaderButtonClicked();

    //分屏
    void onSplitScreenClicked();


public:
    // 提供对数据管理器的访问（用于测试和外部集成）
    FcDataManager* m_dataManager;
    FcDataParser* m_dataParser;
    void updateSchemeList();

private:

    //从远端下载xml方案文件并更新方案列表
    void initSchemeList();

    void initTimeList();
    //存储时间列表数据
    QList<HistoryDataStore::HistoryDataItem> m_currentTimeDataList;

    void setupConnections();
    void setupLeftPanel();
    void setupRightPanel();
    void setupToolBar();

    // 界面创建方法
    QTreeView* createDataTreeView();
    QStandardItemModel* createDataModel(const QString& schemeName);

    // 数据展示方法
    void loadSchemeDataView(const QString& schemeName);
    void updateTreeViewData();

    void buildTreeModel(QStandardItemModel* model, const FcSchemeData& scheme);
    void addUnitMessageToModel(QStandardItemModel* model, const FcUnitMessageData& unitMessage);
    void addTopicToModel(QStandardItem* parentItem, const FcTopicData& topic, const QString& messageId,
                        int sourceFuncId, int pubSubTopicId);
    void addHeadToModel(QStandardItem* parentItem, const FcTopicData& topic, const QString& messageId,
                        int sourceFuncId, int pubSubTopicId);
    void addSignalToModel(QStandardItem* parentItem, const FcSignalData& signal, const QString& topicId,
                         int sourceFuncId, int pubSubTopicId);
    QStandardItem* addSignalRecursive(QStandardItem* parentItem, const QStringList& pathParts, const FcSignalData& signal,
                                     int sourceFuncId, int pubSubTopicId);
    void updateTopicItems(QStandardItem* messageItem, FcUnitMessageData* unitMessage);
    void updateSignalItems(QStandardItem* topicItem, FcTopicData* topic);
    void updateSignalItemsRecursive(QStandardItem* parentItem, FcTopicData* topic);

    // TCP数据更新相关方法
    void updateSignalValueFromTcp(int sourceFuncId, int pubSubTopicId, const QString& signalId, const QString& signalValue);

    // 订阅规则管理方法
    QList<SubRuleManager::SubscribeRule> getSchemeSubscribeRules(const QString& schemeName);
    void updateSubscriptionRules(const QString& schemeName);

    // 搜索和过滤
    void performSearch(const QString& searchText);
    void filterTreeView(const QString& filter);

    // 工具方法
    void centerTabBar();
    QListWidgetItem* createSchemeListItem(const QString& schemeName, bool isActive = false);
    void optimizeTreeViewPerformance(QTreeView* treeView);
    void clearOldData();
    void cleanupResources(const QString& schemeName);

    // 树形结构展开/收缩方法
    void expandAllLevels(QTreeView* treeView, QStandardItemModel* model);
    void collapseToMessageLevel(QTreeView* treeView, QStandardItemModel* model);
    
    // 帧头可见性控制方法
    void setFrameHeaderVisibility(QTreeView* treeView, QStandardItemModel* model, bool hidden);

    // 状态管理
    void updateMonitoringState();
    void updateTreeViewFromResponseData(const QMap<QString, QVector<QString>>& dataMap,
                                                         const QSet<QString>& updatedKeys);
    QString formatTimestamp(const QDateTime& timestamp);
    QString formatDataValue(const QString& value, const QString& dataType);

    void downloadAndSaveXmlFile(const QString& userId, const QString& filePath,
                                std::function<void(bool success)> callback);

    //使用后台请求返回的url去minio下载文件
    void downloadAndSaveFile(const QString& fileName, const QString& filePath,
                             std::function<void(bool success)> callback);

    void sendCurrentSchemeData(SUBSCRIBE_COMMAND cmdMand);

private:
    Ui::FcMonitorWidget *ui;

    // 右侧面板组件
    QMap<QString, QTreeView*> m_schemeTreeViews;      // 方案名 -> 树视图
    QMap<QString, QStandardItemModel*> m_schemeModels; // 方案名 -> 数据模型
    UnitComboDelegate* m_unitDelegate;                 // 单位下拉框委托

    QLabel* m_statusLabel;
    QLabel* m_statisticsLabel;

    // 状态管理
    QString m_currentSchemeName;
    bool m_isMonitoring;
    bool m_isPaused;
    QTimer* m_updateTimer;
    bool m_isExpanded;  // 树形结构展开状态
    bool m_showHeader;  // 帧头显示状态

    // 统计信息
    int m_totalMessages;
    int m_activeMessages;
    int m_totalTopics;
    int m_activeTopics;
    QDateTime m_startTime;

    // 方案管理
    SchemeAddDialog* addSchemeDialog;
    QListWidget *timeListWidget;  // 新增时间列表控件

    // 性能优化
    static const int UPDATE_INTERVAL = 100;  // 更新间隔（毫秒）
    static const int MAX_DISPLAY_ITEMS = 1000; // 最大显示项目数

    //xml minio地址
    QString m_loadXmlUrl;
    bool m_isDownload; //是否在模板界面点击下载

    // 订阅规则管理
    QList<SubRuleManager::SubscribeRule> m_lastRules;  // 上次的订阅规则
    int m_currentBenchId;                              // 当前实验台号
};

#endif // FC_MONITOR_WIDGET_H

