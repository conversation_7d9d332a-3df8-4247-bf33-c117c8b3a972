#ifndef TCP_CLIENT_H
#define TCP_CLIENT_H

#include <QObject>
#include <QTcpSocket>
#include <QHostAddress>
#include <QMutex>
#include <QSettings>
#include <QStandardPaths>
#include <QDir>
#include <QString>
#include <QByteArray>
#include <QDataStream>
#include <QDebug>
#include <QTimer>

#include "third/tcpFrameStruct.h"

/**
 * @brief TCP 客户端类
 * 
 * 该类负责与服务器建立 TCP 连接，并处理数据的发送和接收。
 * 使用单例模式设计，确保系统中只有一个 TCP 客户端实例。
 * IP 地址和端口从配置文件获取。
 */
class TcpClient : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 实验台连接信息结构
     */
    struct BenchConnection {
        QTcpSocket* socket;             // TCP 套接字
        QString serverIp;               // 服务器 IP 地址
        quint16 serverPort;             // 服务器端口
        int reconnectAttempts;          // 当前重连尝试次数
        QTimer* reconnectTimer;         // 重连定时器
        QByteArray receiveBuffer;       // 接收缓冲区，用于处理粘包问题
        bool isConnecting;              // 是否正在连接中

        BenchConnection() : socket(nullptr), serverPort(0), reconnectAttempts(0),
                           reconnectTimer(nullptr), isConnecting(false) {}
    };

    /**
     * @brief 获取单例实例
     * @return TcpClient 的单例对象引用
     */
    static TcpClient& getInstance();

    // 禁止拷贝和移动
    TcpClient(const TcpClient&) = delete;
    TcpClient& operator=(const TcpClient&) = delete;
    TcpClient(TcpClient&&) = delete;
    TcpClient& operator=(TcpClient&&) = delete;

    /**
     * @brief 连接到指定实验台的服务器
     * @param benchId 实验台号
     * @return 是否成功连接
     */
    bool connectToServer(int benchId);

    /**
     * @brief 连接到服务器（兼容旧接口，使用当前默认实验台）
     * @return 是否成功连接
     */
    bool connectToServer();

    /**
     * @brief 断开指定实验台的连接
     * @param benchId 实验台号
     */
    void disconnectFromServer(int benchId);

    /**
     * @brief 断开与服务器的连接（兼容旧接口，断开所有连接）
     */
    void disconnectFromServer();

    /**
     * @brief 发送数据到指定实验台
     * @param benchId 实验台号
     * @param data 要发送的数据
     * @return 是否成功发送
     */
    bool sendData(int benchId, const QByteArray& data);

    /**
     * @brief 发送数据（兼容旧接口，使用当前默认实验台）
     * @param data 要发送的数据
     * @return 是否成功发送
     */
    bool sendData(const QByteArray& data);

    /**
     * @brief 从配置文件重新加载 IP 和端口
     */
    void reloadFromConfig();

    /**
     * @brief 设置服务器 IP 地址
     * @param ip 服务器 IP 地址
     */
    void setServerIp(const QString& ip);

    /**
     * @brief 设置服务器端口
     * @param port 服务器端口
     */
    void setServerPort(quint16 port);

    /**
     * @brief 获取服务器 IP 地址
     * @return 服务器 IP 地址
     */
    QString getServerIp() const;

    /**
     * @brief 获取服务器端口
     * @return 服务器端口
     */
    quint16 getServerPort() const;

    /**
     * @brief 检查指定实验台是否已连接
     * @param benchId 实验台号
     * @return 是否已连接
     */
    bool isConnected(int benchId) const;

    /**
     * @brief 检查是否已连接（兼容旧接口，检查当前默认实验台）
     * @return 是否已连接
     */
    bool isConnected() const;

    /**
     * @brief 获取当前默认实验台号
     * @return 当前默认实验台号
     */
    int getCurrentBenchId() const;

    /**
     * @brief 设置当前默认实验台号
     * @param benchId 实验台号
     */
    void setCurrentBenchId(int benchId);

    //切换实验台号（兼容旧接口）
    int updaeteBench(int benchID);

signals:
    /**
     * @brief 接收到数据的信号
     * @param data 接收到的数据
     */
    void dataReceived(const QByteArray& data);

    /**
     * @brief 连接状态改变的信号
     * @param connected 是否已连接
     */
    void connectionStatusChanged(bool connected);

    /**
     * @brief 连接错误的信号
     * @param errorMsg 错误信息
     */
    void connectionError(const QString& errorMsg);
    
    /**
     * @brief 命令处理结果转发信号
     * 
     * 这个信号用于从TcpDataProcessor转发命令处理结果
     * 作为中转，保持数据流的统一入口和出口
     * 
     * @param cmdType 命令类型
     * @param success 处理是否成功
     * @param message 处理结果消息
     */
    void processingResult(SUBSCRIBE_COMMAND cmdType, bool success, const QString &message);
    
    /**
     * @brief 命令处理错误转发信号
     * 
     * 这个信号用于从TcpDataProcessor转发命令处理错误
     * 作为中转，保持数据流的统一入口和出口
     * 
     * @param cmdType 命令类型
     * @param errorMessage 错误消息
     */
    void processingError(SUBSCRIBE_COMMAND cmdType, const QString &errorMessage);

private slots:
    /**
     * @brief 处理接收到的数据
     */
    void onReadyRead();

    /**
     * @brief 处理连接状态改变
     */
    void onConnected();

    /**
     * @brief 处理断开连接
     */
    void onDisconnected();

    /**
     * @brief 处理连接错误
     * @param socketError 套接字错误
     */
    void onError(QAbstractSocket::SocketError socketError);

    /**
     * @brief 尝试重新连接
     */
    void tryReconnect();

private:
    /**
     * @brief 私有构造函数，防止外部创建实例
     */
    TcpClient();
    
    /**
     * @brief 析构函数
     */
    ~TcpClient();

    /**
     * @brief 加载配置
     */
    void loadConfig(int benchId);

    /**
     * @brief 保存配置
     */
    void saveConfig();

    /**
     * @brief 确保配置文件存在
     */
    void ensureConfigExists();

    /**
     * @brief 获取或创建指定实验台的连接
     * @param benchId 实验台号
     * @return 连接信息指针
     */
    BenchConnection* getOrCreateConnection(int benchId);

    /**
     * @brief 清理指定实验台的连接
     * @param benchId 实验台号
     */
    void cleanupConnection(int benchId);

    /**
     * @brief 设置连接的信号槽
     * @param connection 连接信息
     * @param benchId 实验台号
     */
    void setupConnectionSignals(BenchConnection* connection, int benchId);



    QMap<int, BenchConnection*> m_benchConnections; // 实验台号 -> 连接信息映射
    mutable QMutex m_mutex;             // 互斥锁，用于线程安全
    bool m_reconnectEnabled;            // 是否启用重连
    int m_reconnectInterval;            // 重连间隔（毫秒）
    int m_maxReconnectAttempts;         // 最大重连尝试次数
    int m_currentBenchId;               // 当前默认实验台号

    // 临时兼容变量（用于loadConfig等方法）
    QString m_serverIp;                 // 临时服务器 IP 地址
    quint16 m_serverPort;               // 临时服务器端口
};

#endif // TCP_CLIENT_H 
