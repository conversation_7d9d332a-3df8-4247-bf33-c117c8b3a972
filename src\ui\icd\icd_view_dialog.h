﻿#ifndef ICD_VIEW_DIALOG_H
#define ICD_VIEW_DIALOG_H

#include <QWidget>
#include <QStandardItemModel>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFrame>
#include <QDir>
#include <QFileInfo>
#include <QSplitter>
#include <QTreeView>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>
#include <QStack>
#include <QDialog>
#include <QListWidget>
#include <QTextEdit>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>

namespace Ui {
class ICDViewDialog;
}

class ICDViewDialog : public QWidget
{
    Q_OBJECT

public:
    explicit ICDViewDialog(QWidget *parent = nullptr);
    ~ICDViewDialog();

signals:
    void fileListUpdated();  // 文件列表更新信号

private slots:
    void onSearchTextChanged(const QString &text);
    void onViewFile(const QString &filePath);
    void onDeleteFile(const QString &filePath);
    void onAddFileClicked();
    void onImportFileClicked();
    void onCloseButtonClicked();
    void onSaveButtonClicked();
    void onUploadFinished(QNetworkReply *reply);

private:
    void setupUI();
    void setupConnections();
    void loadICDFileList();
    void filterFileList(const QString &text);
    void addFileItem(const QString &filePath);
    void showFileContent(const QString &filePath);
    bool saveXmlFile(const QString &filePath);
    void uploadToServer(const QString &content, const QString &fileName);

    //使用正则匹配获取icd文件的版本信息，三楼在库的接口中获取，2楼修改了接口需要补充该功能
    QString extractVersionNumberXml(const QString &input);

    Ui::ICDViewDialog *ui;
    
    // 主要组件
    QLabel *titleLabel;
    QPushButton *addButton;
    QPushButton *importButton;
    QPushButton *closeButton;
    QFrame *leftSeparatorLine;
    QSplitter *mainSplitter;
    
    // 左侧组件
    QLabel *leftTitleLabel;
    QLineEdit *searchEdit;
    QListWidget *fileListWidget;
    
    // 右侧组件
    QLabel *contentLabel;
    QTreeView *contentTreeView;
    QTextEdit *contentTextEdit;
    QPushButton *saveButton;
    
    // 存储文件路径列表
    QStringList icdFilePaths;
    
    // 当前选中的文件路径
    QString currentFilePath;

    // 网络请求管理器
    QNetworkAccessManager *networkManager;
};

#endif // ICD_VIEW_DIALOG_H
