#ifndef SUB_RULE_MANAGER_H
#define SUB_RULE_MANAGER_H

#include <QObject>
#include <QMap>
#include <QSet>
#include <QMutex>
#include <QMutexLocker>
#include <QDebug>
#include "third/tcpFrameStruct.h"

/**
 * @brief 订阅规则管理器
 * 
 * 该类负责管理所有FcMonitorWidget实例的订阅规则，使用引用计数机制
 * 确保多个界面可以独立订阅和取消订阅，而不会相互影响。
 */
class SubRuleManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return SubRuleManager 的单例对象引用
     */
    static SubRuleManager& getInstance();

    // 禁止拷贝和移动
    SubRuleManager(const SubRuleManager&) = delete;
    SubRuleManager& operator=(const SubRuleManager&) = delete;
    SubRuleManager(SubRuleManager&&) = delete;
    SubRuleManager& operator=(SubRuleManager&&) = delete;

    /**
     * @brief 订阅规则结构
     */
    struct SubscribeRule {
        uint32_t sourceId;  // 源ID
        uint32_t topicId;   // 主题ID
        
        bool operator==(const SubscribeRule& other) const {
            return sourceId == other.sourceId && topicId == other.topicId;
        }
        
        bool operator<(const SubscribeRule& other) const {
            if (sourceId != other.sourceId) {
                return sourceId < other.sourceId;
            }
            return topicId < other.topicId;
        }
    };

    /**
     * @brief 添加订阅规则
     * @param widgetId 界面ID（通常使用FcMonitorWidget的指针地址）
     * @param benchId 实验台号
     * @param rules 订阅规则列表
     */
    void addRules(void* widgetId, int benchId, const QList<SubscribeRule>& rules);

    /**
     * @brief 删除订阅规则
     * @param widgetId 界面ID
     * @param benchId 实验台号
     * @param rules 订阅规则列表
     */
    void delRules(void* widgetId, int benchId, const QList<SubscribeRule>& rules);

    /**
     * @brief 更新界面的所有订阅规则（先删除旧规则，再添加新规则）
     * @param widgetId 界面ID
     * @param benchId 实验台号
     * @param oldRules 旧的订阅规则列表
     * @param newRules 新的订阅规则列表
     */
    void updateRules(void* widgetId, int benchId, 
                     const QList<SubscribeRule>& oldRules, 
                     const QList<SubscribeRule>& newRules);

    /**
     * @brief 清除指定界面的所有订阅规则
     * @param widgetId 界面ID
     */
    void clearWidgetRules(void* widgetId);

    /**
     * @brief 获取指定实验台的当前有效订阅规则
     * @param benchId 实验台号
     * @return 当前有效的订阅规则列表
     */
    QList<SubscribeRule> getCurrentRules(int benchId) const;

    /**
     * @brief 注册界面实例
     * @param widgetId 界面ID
     * @param widget 界面指针（用于调试和管理）
     */
    void registerWidget(void* widgetId, QObject* widget);

    /**
     * @brief 注销界面实例
     * @param widgetId 界面ID
     */
    void unregisterWidget(void* widgetId);

signals:
    /**
     * @brief 订阅规则变化信号
     * @param benchId 实验台号
     * @param addedRules 新增的规则
     * @param removedRules 移除的规则
     */
    void rulesChanged(int benchId, const QList<SubscribeRule>& addedRules, 
                      const QList<SubscribeRule>& removedRules);

private:
    /**
     * @brief 私有构造函数
     */
    SubRuleManager();

    /**
     * @brief 析构函数
     */
    ~SubRuleManager();

    /**
     * @brief 发送订阅更新到TcpClient
     * @param benchId 实验台号
     * @param addedRules 新增的规则
     * @param removedRules 移除的规则
     */
    void sendSubscriptionUpdate(int benchId, const QList<SubscribeRule>& addedRules, 
                               const QList<SubscribeRule>& removedRules);

    /**
     * @brief 将SubscribeRule转换为SUBSCRIBE_PACKAGE_SUB_RULE
     */
    SUBSCRIBE_PACKAGE_SUB_RULE toTcpRule(const SubscribeRule& rule) const;

    /**
     * @brief 规则引用计数结构
     */
    struct RuleRefCount {
        SubscribeRule rule;
        int refCount;
        QSet<void*> widgets; // 引用该规则的界面集合
        
        RuleRefCount() : refCount(0) {}
        RuleRefCount(const SubscribeRule& r) : rule(r), refCount(0) {}
    };

    // 数据成员
    mutable QMutex m_mutex;                                    // 线程安全锁
    QMap<int, QMap<SubscribeRule, RuleRefCount>> m_benchRules; // 实验台号 -> 规则引用计数映射
    QMap<void*, QObject*> m_registeredWidgets;                // 已注册的界面映射
    QMap<void*, QMap<int, QList<SubscribeRule>>> m_widgetRules; // 界面ID -> 实验台号 -> 规则列表
};

// 为了支持QMap中使用SubscribeRule作为key，需要定义hash函数
inline uint qHash(const SubRuleManager::SubscribeRule& rule, uint seed = 0)
{
    return qHash(rule.sourceId, seed) ^ qHash(rule.topicId, seed + 1);
}

#endif // SUB_RULE_MANAGER_H
