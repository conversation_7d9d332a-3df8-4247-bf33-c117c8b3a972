#include "file_list_service.h"
#include "user_session.h"
#include "api_url_manager.h"
#include "third/parseAndAnalysis.h"

#include <QDebug>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QNetworkRequest>
#include <QUrl>
#include <QMutexLocker>
#include <QStandardPaths>
#include <QDir>
#include <QSettings>
#include <QCoreApplication>

/**
 * 获取单例实例
 */
FileListService& FileListService::getInstance()
{
    static FileListService instance;
    return instance;
}

/**
 * 私有构造函数
 */
FileListService::FileListService() : QObject(nullptr), m_hasFileList(false)
{
    m_networkManager = new QNetworkAccessManager(this);
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &FileListService::handleNetworkReply);

    // 加载默认选中的ICD文件
    loadDefaultSelectedIcdFile();
    createParseandanalysisP(m_defaultSelectedIcdFile);

    qDebug() << "FileListService initialized";
}

/**
 * 从服务器获取文件列表
 */
void FileListService::fetchFileList(std::function<void(const QStringList&, bool)> callback)
{
    QMutexLocker locker(&m_mutex);

    // 如果已经有文件列表且不需要刷新，直接返回缓存的列表
    if (m_hasFileList) {
        QStringList fileListCopy = m_fileList;
        locker.unlock();
        callback(fileListCopy, true);
        return;
    }

    // 添加回调到等待列表
    m_pendingCallbacks.append(callback);

    // 发起网络请求
    QNetworkRequest request(ApiUrlManager::getInstance().getUrl(ApiUrlManager::ICD_LIST));
    request.setRawHeader("accept", "*/*");
    QString token = UserSession::getInstance().getToken();
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }
    qDebug() << "Sending file list request to server...";
    m_networkManager->get(request);
}

/**
 * 获取最近一次获取的文件列表
 */
QStringList FileListService::getFileList() const
{
    QMutexLocker locker(&m_mutex);
    return m_fileList;
}

/**
 * 检查是否已经获取过文件列表
 */
bool FileListService::hasFileList() const
{
    QMutexLocker locker(&m_mutex);
    return m_hasFileList;
}

/**
 * 强制刷新文件列表
 */
void FileListService::refreshFileList(std::function<void(const QStringList&, bool)> callback)
{
    QMutexLocker locker(&m_mutex);

    // 清除缓存标志，强制重新获取
    m_hasFileList = false;
    
    // 添加回调到等待列表
    m_pendingCallbacks.append(callback);
    QNetworkRequest request(ApiUrlManager::getInstance().getUrl(ApiUrlManager::ICD_LIST));
    request.setRawHeader("accept", "*/*");
    QString token = UserSession::getInstance().getToken();
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }
    qDebug() << "Sending file list refresh request to server...";
    m_networkManager->get(request);
}

/**
 * 获取指定文件名的详细信息
 */
FileItem FileListService::getFileItemByName(const QString& fileName) const
{
    QMutexLocker locker(&m_mutex);
    return m_fileItems.value(fileName, FileItem());
}

/**
 * 获取所有文件的详细信息
 */
QMap<QString, FileItem> FileListService::getAllFileItems() const
{
    QMutexLocker locker(&m_mutex);
    return m_fileItems;
}

/**
 * 获取默认选中的ICD文件名
 */
QString FileListService::getDefaultSelectedIcdFile() const
{
    QMutexLocker locker(&m_mutex);
    return m_defaultSelectedIcdFile;
}

/**
 * 设置默认选中的ICD文件名
 */
void FileListService::setDefaultSelectedIcdFile(const QString& fileName)
{
    {
        QMutexLocker locker(&m_mutex);
        // 如果文件名相同，不做任何操作
        if (m_defaultSelectedIcdFile == fileName) {
            return;
        }
        
        // 更新默认选中的ICD文件名
        m_defaultSelectedIcdFile = fileName;
    }
    
    // 保存到配置文件
    saveDefaultSelectedIcdFile();
    
    // 发出信号通知其他组件
    emit defaultSelectedIcdFileChanged(fileName);
    
    qDebug() << "Default selected ICD file changed to:" << fileName;
}


/**
* 获取icd版本
*/
std::string FileListService::GetICDVersion()
{
    return m_icd_version;
}

/**
 * 生成操作icd解析库的操作指针
 */
int FileListService::createParseandanalysisP(const QString& fileName)
{
    // 解析文件生成动态库指针
    void *dllP = nullptr;
    int ret = 0;

    QString icdDirPath = QCoreApplication::applicationDirPath()+"/icd_files/";
    QString icdFile = icdDirPath + fileName;
    // QString jsonFile = icdDirPath + QFileInfo(fileName).completeBaseName() + ".json";
    std::string icdVersion;

    ret = loadIcd(icdFile.toStdString());
    std::string jsonFile = icdDirPath.toStdString() + icdVersion + ".json";
    ret = initDecoder(dllP);

    if(!dllP) {
        return -1;
    }

    if (m_parseandanalysisP){
        destroyDecoder(m_parseandanalysisP);
    }
    m_parseandanalysisP = dllP;

    return 0;
}

/**
     * @brief 获取操作icd解析库的操作指针
*/
void* FileListService::getParseandanalysisP()
{
    return m_parseandanalysisP;
}

/**
 * 从配置文件中加载默认选中的ICD文件
 */
void FileListService::loadDefaultSelectedIcdFile()
{
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");
    
    // 确保配置目录存在
    QDir().mkpath(configPath);
    
    // 从配置文件中读取默认选中的ICD文件
    QSettings settings(configFile, QSettings::IniFormat);
    QString defaultIcdFile = settings.value("ICD/defaultSelectedFile", "").toString();
    
    QMutexLocker locker(&m_mutex);
    m_defaultSelectedIcdFile = defaultIcdFile;
    
    qDebug() << "Loaded default selected ICD file from config:" << m_defaultSelectedIcdFile;
}

/**
 * 将默认选中的ICD文件保存到配置文件
 */
void FileListService::saveDefaultSelectedIcdFile()
{
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");
    
    // 确保配置目录存在
    QDir().mkpath(configPath);
    
    // 保存默认选中的ICD文件到配置文件
    QSettings settings(configFile, QSettings::IniFormat);
    
//    QMutexLocker locker(&m_mutex);
    settings.setValue("ICD/defaultSelectedFile", m_defaultSelectedIcdFile);
    settings.sync();
    
    qDebug() << "Saved default selected ICD file to config:" << m_defaultSelectedIcdFile;
}

/**
 * 处理网络请求的响应
 */
void FileListService::handleNetworkReply(QNetworkReply* reply)
{
    // 获取所有等待的回调
    QList<std::function<void(const QStringList&, bool)>> callbacks;
    QStringList fileList;
    bool success = false;

    {
        QMutexLocker locker(&m_mutex);
        callbacks = m_pendingCallbacks;
        m_pendingCallbacks.clear();
    }

    if (reply->error() == QNetworkReply::NoError) {
        // 读取响应数据
        QByteArray responseData = reply->readAll();

        // 尝试解析JSON响应
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);

        if (!jsonDoc.isNull()) {
            qDebug() << "File list response received:" << responseData;

            if (jsonDoc.isObject()) {
                // 如果响应是一个JSON对象
                QJsonObject jsonObj = jsonDoc.object();

                // 检查响应是否成功
                bool isSuccess = jsonObj.value("success").toBool(false);
                QString code = jsonObj.value("code").toString();
                QString message = jsonObj.value("message").toString();

                qDebug() << "Response status: success=" << isSuccess << ", code=" << code << ", message=" << message;

                // 检查是否有data字段且为数组
                if (isSuccess && code == "200" && jsonObj.contains("data") && jsonObj["data"].isArray()) {
                    QJsonArray jsonArray = jsonObj["data"].toArray();

                    // 清空现有数据
                    fileList.clear();
                    QMap<QString, FileItem> fileItems;

                    // 解析每个文件项
                    for (int i = 0; i < jsonArray.size(); ++i) {
                        if (jsonArray[i].isObject()) {
                            QJsonObject itemObj = jsonArray[i].toObject();
                            
                            // 创建并填充文件项结构体
                            FileItem item;
                            item.id = itemObj.value("id").toInt();
                            item.icdName = itemObj.value("icdName").toString();
                            item.icdVersion = itemObj.value("icdVersion").toString();
                            item.userVersion = itemObj.value("userVersion").toString();
                            item.url = itemObj.value("url").toString();
                            item.componentVos = itemObj.value("componentVos").toVariant();
                            
                            // 使用icdName作为键，保存到映射中
                            if (!item.icdName.isEmpty()) {
                                fileList.append(item.icdName);
                                fileItems.insert(item.icdName, item);
                            }
                        }
                    }

                    qDebug() << "Parsed file list:" << fileList;
                    qDebug() << "Total file items:" << fileItems.size();
                    
                    // 更新缓存的文件列表和文件项
                    QMutexLocker locker(&m_mutex);
                    m_fileList = fileList;
                    m_fileItems = fileItems;
                    m_hasFileList = true;
                    success = true;
                    
                    // 检查默认选中的ICD文件是否在文件列表中
                    if (!m_defaultSelectedIcdFile.isEmpty() && !fileList.contains(m_defaultSelectedIcdFile)) {
                        qWarning() << "Default selected ICD file" << m_defaultSelectedIcdFile << "not found in file list";
                        // 如果默认选中的文件不在列表中，可以选择清除默认选中
                        m_defaultSelectedIcdFile = "";
                        saveDefaultSelectedIcdFile();
                    }
                } else {
                    qWarning() << "File list response is invalid or contains error";
                }
            } else {
                qWarning() << "File list response is not a valid JSON object";
            }
        } else {
            qWarning() << "Failed to parse file list response as JSON:" << responseData;
        }
    } else {
        qWarning() << "File list request failed:" << reply->errorString();
    }

    // 调用所有等待的回调
    for (const auto& callback : callbacks) {
        callback(fileList, success);
    }

    // 释放响应对象
    reply->deleteLater();
}

FileListService::~FileListService()
{
    if (m_parseandanalysisP) {
        destroyDecoder(m_parseandanalysisP);
        m_parseandanalysisP = nullptr;
    }
}
