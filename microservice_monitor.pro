QT       += core gui network xml concurrent testlib charts

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = microservice_monitor
TEMPLATE = app

CONFIG += c++11
CONFIG += console

# 禁用 Qt 5.15.2 中的弃用警告
DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x050F00

# 启用测试模式的条件编译
# 取消注释以启用测试模式
# DEFINES += RUN_TESTS

# 定义DLL动态加载方式
#DEFINES += PARSE_ANALYSIS_DLL_LOAD

# 如果要编译独立的测试程序，取消下面的注释
# DEFINES += STANDALONE_TEST

# 注释掉静态链接库，将使用动态加载方式
# LIBS += -L$$PWD/third -lparseAndAnalysis
win32: LIBS += -L$$PWD/third/ -llibparseAndAnalysis.dll

SOURCES += \
    src/main.cpp \
#    src/parseandanalysis/dll_parseandanalysis.cpp \
    src/ui/FcMonitor/fc_data_manager.cpp \
    src/ui/FcMonitor/fc_data_parser.cpp \
    src/ui/FcMonitor/fc_monitor.cpp \
    src/ui/FcMonitor/fc_monitor_widget.cpp \
    src/ui/FcMonitor/unit_standard_item.cpp \
    src/ui/FcMonitor/unit_combo_delegate.cpp \
#    src/ui/graph/graph_monitor.cpp \
#    src/ui/graph/graph_scheme_add_dialog.cpp \
#    src/ui/graph/graph_scheme_edit_dialog.cpp \
#    src/ui/graph/graph_scheme_list_item.cpp \
#    src/ui/graph/graph_scheme_manager.cpp \
    src/ui/login/login_window.cpp \
    src/ui/document/document_window.cpp \
    src/ui/main/main_window.cpp \
    src/ui/monitor/monitor_window.cpp \
    src/ui/scheme/history_data_store.cpp \
#    src/ui/scheme/scheme_add_dialog.cpp \
    src/ui/scheme/scheme_list_item.cpp \
    src/ui/scheme/solution.cpp \
    src/ui/scheme/template_list_item.cpp \
    src/ui/scheme/template_list_widget.cpp \
    src/ui/user/user_management.cpp \
    src/ui/user/user_dialog.cpp \
    src/ui/user/reset_dialog.cpp \
    src/utils/file_utils.cpp \
    src/utils/unit_model.cpp \
    src/utils/user_session.cpp \
    src/utils/file_list_service.cpp \
    src/utils/api_url_manager.cpp \
    src/utils/tcp_client.cpp \
    src/utils/tcp_data_processor.cpp \
    src/utils/response_data_store.cpp \
    src/utils/sub_rule_manager.cpp \
    src/utils/command_handlers/command_handler.cpp \
    src/utils/command_handlers/subscribe_handler.cpp \
    src/utils/command_handlers/unsubscribe_handler.cpp \
    src/utils/command_handlers/suspend_handler.cpp \
    src/utils/command_handlers/continue_handler.cpp \
    src/utils/command_handlers/response_handler.cpp \
    src/ui/permission/permission_management.cpp \
    src/ui/scheme/scheme_add_dialog.cpp \
#    src/ui/scheme/scheme_management.cpp \
    src/ui/scheme/scheme_edit_dialog.cpp \
    src/ui/icd/icd_view_dialog.cpp \
    src/ui/unitAllocation/unitallocation.cpp \
    src/ui/unitAllocation/add_type_dialog.cpp \
    src/ui/common/datetime_picker_dialog.cpp

HEADERS += \
#    src/parseandanalysis/dll_parseandanalysis.h \
    src/ui/FcMonitor/fc_data_manager.h \
    src/ui/FcMonitor/fc_data_parser.h \
    src/ui/FcMonitor/fc_data_structures.h \
    src/ui/FcMonitor/fc_monitor.h \
    src/ui/FcMonitor/fc_monitor_widget.h \
    src/ui/FcMonitor/unit_standard_item.h \
    src/ui/FcMonitor/unit_combo_delegate.h \
#    src/ui/graph/graph_monitor.h \
#    src/ui/graph/graph_scheme_add_dialog.h \
#    src/ui/graph/graph_scheme_edit_dialog.h \
#    src/ui/graph/graph_scheme_list_item.h \
#    src/ui/graph/graph_scheme_manager.h \
    src/ui/login/login_window.h \
    src/ui/document/document_window.h \
    src/ui/main/main_window.h \
    src/ui/monitor/monitor_window.h \
#    src/ui/scheme/data_join_type.h \
    src/ui/scheme/history_data_store.h \
    src/ui/scheme/scheme_add_dialog.h \
    src/ui/scheme/scheme_list_item.h \
    src/ui/scheme/solution.h \
    src/ui/scheme/template_list_item.h \
    src/ui/scheme/template_list_widget.h \
    src/ui/user/user_management.h \
    src/ui/user/user_dialog.h \
    src/ui/user/reset_dialog.h \
    src/utils/file_utils.h \
    src/utils/unit_model.h \
    src/utils/user_session.h \
    src/utils/file_list_service.h \
    src/utils/api_url_manager.h \
    src/utils/tcp_data_processor.h \
    src/utils/tcp_client.h \
    src/utils/sub_rule_manager.h \
    src/utils/response_data_store.h \
    src/utils/command_handlers/command_handler.h \
    src/utils/command_handlers/subscribe_handler.h \
    src/utils/command_handlers/unsubscribe_handler.h \
    src/utils/command_handlers/suspend_handler.h \
    src/utils/command_handlers/continue_handler.h \
    src/utils/command_handlers/response_handler.h \
    src/ui/permission/permission_management.h \
#    src/ui/scheme/scheme_management.h \
    src/ui/scheme/scheme_edit_dialog.h \
    src/ui/icd/icd_view_dialog.h \
    src/ui/unitAllocation/unitallocation.h \
    src/ui/unitAllocation/add_type_dialog.h \
    src/ui/common/datetime_picker_dialog.h \
    third/frameStruct.h \
    third/icdStructDef.h \
    third/parseAndAnalysis.h \
    third/tcpFrameStruct.h

FORMS += \
    src/ui/FcMonitor/fc_monitor.ui \
    src/ui/FcMonitor/fc_monitor_widget.ui \
#    src/ui/graph/graph_monitor.ui \
#    src/ui/graph/graph_scheme_add_dialog.ui \
#    src/ui/graph/graph_scheme_edit_dialog.ui \
#    src/ui/graph/graph_scheme_list_item.ui \
#    src/ui/graph/graph_scheme_manager.ui \
    src/ui/login/loginwindow.ui \
    src/ui/document/documentwindow.ui \
    src/ui/main/mainwindow.ui \
    src/ui/monitor/monitorwindow.ui \
    src/ui/scheme/scheme_add_dialog.ui \
    src/ui/scheme/scheme_list_item.ui \
    src/ui/scheme/template_list_item.ui \
    src/ui/scheme/template_list_widget.ui \
    src/ui/user/usermanagement.ui \
    src/ui/permission/permissionmanagement.ui \
#    src/ui/scheme/schememanagement.ui \
    src/ui/scheme/scheme_edit_dialog.ui \
    src/ui/icd/icd_view_dialog.ui \
    src/ui/unitAllocation/unitAllocation.ui \
    src/ui/common/datetime_picker_dialog.ui

RESOURCES += \
    src/resources/resources.qrc

INCLUDEPATH += \
    src \
    third \
    src/ui/scheme \
    src/ui/FcMonitor

DEPENDPATH += $$PWD/third

# 确保在Windows上正确部署DLL
win32 {
    DESTDIR = $$OUT_PWD/debug
    CONFIG(release, debug|release): DESTDIR = $$OUT_PWD/release
    
    # 复制DLL到生成目录 - 修复路径中特殊字符的问题
    DLL_SOURCE = $$shell_quote($$shell_path($$PWD/third/libparseAndAnalysis.dll))
    DLL_DEST = $$shell_quote($$shell_path($$DESTDIR))
    
    # 使用PowerShell命令来复制文件，避免cmd的引号问题
    QMAKE_POST_LINK += powershell -Command "Copy-Item -Path $$DLL_SOURCE -Destination $$DLL_DEST -Force"
}
