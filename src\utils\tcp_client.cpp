#include "tcp_client.h"

#include <QNetworkProxy>
#include <QMutexLocker>

/**
 * 获取单例实例
 */
TcpClient& TcpClient::getInstance() {
    static TcpClient instance;
    return instance;
}

/**
 * 私有构造函数
 */
TcpClient::TcpClient() :
    m_reconnectEnabled(true),
    m_reconnectInterval(5000),
    m_maxReconnectAttempts(5),
    m_currentBenchId(1),
    m_serverIp("127.0.0.1"),
    m_serverPort(8080)
{
    qDebug() << "TcpClient initialized with multi-bench support";
}

/**
 * 析构函数
 */
TcpClient::~TcpClient() {
    QMutexLocker locker(&m_mutex);

    // 清理所有连接
    for (auto it = m_benchConnections.begin(); it != m_benchConnections.end(); ++it) {
        BenchConnection* conn = it.value();
        if (conn) {
            if (conn->socket && conn->socket->state() == QAbstractSocket::ConnectedState) {
                conn->socket->disconnectFromHost();
                if (conn->socket->state() != QAbstractSocket::UnconnectedState) {
                    conn->socket->waitForDisconnected(1000);
                }
            }
            if (conn->reconnectTimer) {
                conn->reconnectTimer->stop();
            }
            delete conn;
        }
    }
    m_benchConnections.clear();

    qDebug() << "TcpClient destroyed";
}

TcpClient::BenchConnection* TcpClient::getOrCreateConnection(int benchId)
{
    auto it = m_benchConnections.find(benchId);
    if (it != m_benchConnections.end()) {
        return it.value();
    }

    // 创建新连接
    BenchConnection* conn = new BenchConnection();
    conn->socket = new QTcpSocket(this);

    // 防止本地代理影响
    QNetworkProxy noProxy;
    noProxy.setType(QNetworkProxy::NoProxy);
    conn->socket->setProxy(noProxy);

    // 创建重连定时器
    conn->reconnectTimer = new QTimer(this);

    // 加载配置
    loadConfig(benchId);
    conn->serverIp = m_serverIp;
    conn->serverPort = m_serverPort;

    // 设置信号槽连接
    setupConnectionSignals(conn, benchId);

    m_benchConnections[benchId] = conn;

    qDebug() << "Created new connection for bench" << benchId << "IP:" << conn->serverIp << "Port:" << conn->serverPort;
    return conn;
}

void TcpClient::cleanupConnection(int benchId)
{
    auto it = m_benchConnections.find(benchId);
    if (it == m_benchConnections.end()) {
        return;
    }

    BenchConnection* conn = it.value();
    if (conn) {
        if (conn->socket) {
            if (conn->socket->state() == QAbstractSocket::ConnectedState) {
                conn->socket->disconnectFromHost();
                if (conn->socket->state() != QAbstractSocket::UnconnectedState) {
                    conn->socket->waitForDisconnected(1000);
                }
            }
            conn->socket->deleteLater();
        }
        if (conn->reconnectTimer) {
            conn->reconnectTimer->stop();
            conn->reconnectTimer->deleteLater();
        }
        delete conn;
    }

    m_benchConnections.erase(it);
    qDebug() << "Cleaned up connection for bench" << benchId;
}

void TcpClient::setupConnectionSignals(BenchConnection* connection, int benchId)
{
    if (!connection || !connection->socket) {
        return;
    }

    // 连接信号和槽
    connect(connection->socket, &QTcpSocket::readyRead, this, &TcpClient::onReadyRead);
    connect(connection->socket, &QTcpSocket::connected, this, &TcpClient::onConnected);
    connect(connection->socket, &QTcpSocket::disconnected, this, &TcpClient::onDisconnected);
#if QT_VERSION >= QT_VERSION_CHECK(5, 15, 0)
    connect(connection->socket, &QAbstractSocket::errorOccurred, this, &TcpClient::onError);
#else
    connect(connection->socket, SIGNAL(error(QAbstractSocket::SocketError)), this, SLOT(onError(QAbstractSocket::SocketError)));
#endif

    // 连接重连定时器
    connect(connection->reconnectTimer, &QTimer::timeout, this, &TcpClient::tryReconnect);

    // 存储benchId到socket的映射，用于在槽函数中识别是哪个实验台
    connection->socket->setProperty("benchId", benchId);
    connection->reconnectTimer->setProperty("benchId", benchId);
}

/**
 * 连接到指定实验台的服务器
 */
bool TcpClient::connectToServer(int benchId)
{
    BenchConnection* conn = getOrCreateConnection(benchId);
    if (!conn || !conn->socket) {
        qWarning() << "Failed to create connection for bench" << benchId;
        return false;
    }

    if (conn->socket->state() == QAbstractSocket::ConnectedState) {
        qDebug() << "Already connected to server for bench" << benchId;
        return true;
    }

    if (conn->isConnecting) {
        qDebug() << "Connection already in progress for bench" << benchId;
        return false;
    }

    conn->isConnecting = true;
    qDebug() << "Attempting to connect to server for bench" << benchId
             << "IP:" << conn->serverIp << "Port:" << conn->serverPort;

    conn->socket->connectToHost(conn->serverIp, conn->serverPort);

    // 等待连接完成
    bool connected = conn->socket->waitForConnected(5000);
    conn->isConnecting = false;

    if (!connected) {
        qWarning() << "Failed to connect to server for bench" << benchId << ":" << conn->socket->errorString();
        if (m_reconnectEnabled && conn->reconnectAttempts < m_maxReconnectAttempts) {
            conn->reconnectTimer->start(m_reconnectInterval);
            qDebug() << "Will retry connection for bench" << benchId << "in" << m_reconnectInterval / 1000 << "seconds";
        }
    }

    return connected;
}

/**
 * 连接到服务器（兼容旧接口）
 */
bool TcpClient::connectToServer()
{
    return connectToServer(m_currentBenchId);
}

/**
 * 断开指定实验台的连接
 */
void TcpClient::disconnectFromServer(int benchId) {
    QMutexLocker locker(&m_mutex);

    auto it = m_benchConnections.find(benchId);
    if (it == m_benchConnections.end()) {
        qDebug() << "No connection found for bench" << benchId;
        return;
    }

    BenchConnection* conn = it.value();
    if (!conn || !conn->socket) {
        return;
    }

    if (conn->socket->state() != QAbstractSocket::UnconnectedState) {
        qDebug() << "Disconnecting from server for bench" << benchId;
        conn->socket->disconnectFromHost();

        // 等待断开连接完成
        if (conn->socket->state() != QAbstractSocket::UnconnectedState) {
            conn->socket->waitForDisconnected(3000);
        }
    }

    // 停止重连定时器
    if (conn->reconnectTimer && conn->reconnectTimer->isActive()) {
        conn->reconnectTimer->stop();
    }

    // 重置重连尝试次数和状态
    conn->reconnectAttempts = 0;
    conn->isConnecting = false;
    conn->receiveBuffer.clear();
}

/**
 * 断开与服务器的连接（兼容旧接口）
 */
void TcpClient::disconnectFromServer()
{
    QMutexLocker locker(&m_mutex);

    // 断开所有连接
    for (auto it = m_benchConnections.begin(); it != m_benchConnections.end(); ++it) {
        int benchId = it.key();
        locker.unlock();
        disconnectFromServer(benchId);
        locker.relock();
    }
}

/**
 * 发送数据到指定实验台
 */
bool TcpClient::sendData(int benchId, const QByteArray& data) {
    QMutexLocker locker(&m_mutex);

    auto it = m_benchConnections.find(benchId);
    if (it == m_benchConnections.end()) {
        qWarning() << "No connection found for bench" << benchId;
        return false;
    }

    BenchConnection* conn = it.value();
    if (!conn || !conn->socket) {
        qWarning() << "Invalid connection for bench" << benchId;
        return false;
    }

    if (conn->socket->state() != QAbstractSocket::ConnectedState) {
        qWarning() << "Not connected to server for bench" << benchId << ", attempting to connect...";
        locker.unlock();
        if (!connectToServer(benchId)) {
            qWarning() << "Failed to connect to server for bench" << benchId;
            return false;
        }
        locker.relock();
    }

    qint64 bytesWritten = conn->socket->write(data);
    if (bytesWritten != data.size()) {
        qWarning() << "Incomplete data sent for bench" << benchId << "sent:" << bytesWritten << "total:" << data.size();
        return false;
    }

    qDebug() << "Successfully sent data for bench" << benchId << "size:" << bytesWritten << "bytes";
    return true;
}

/**
 * 发送数据（兼容旧接口）
 */
bool TcpClient::sendData(const QByteArray& data) {
    return sendData(m_currentBenchId, data);
}

/**
 * 检查指定实验台是否已连接
 */
bool TcpClient::isConnected(int benchId) const {
    QMutexLocker locker(&m_mutex);

    auto it = m_benchConnections.find(benchId);
    if (it == m_benchConnections.end()) {
        return false;
    }

    BenchConnection* conn = it.value();
    return conn && conn->socket && conn->socket->state() == QAbstractSocket::ConnectedState;
}

/**
 * 检查是否已连接（兼容旧接口）
 */
bool TcpClient::isConnected() const {
    return isConnected(m_currentBenchId);
}

/**
 * 获取当前默认实验台号
 */
int TcpClient::getCurrentBenchId() const {
    QMutexLocker locker(&m_mutex);
    return m_currentBenchId;
}

/**
 * 设置当前默认实验台号
 */
void TcpClient::setCurrentBenchId(int benchId) {
    QMutexLocker locker(&m_mutex);
    m_currentBenchId = benchId;
    qDebug() << "Set current bench ID to" << benchId;
}

/**
 * 处理接收到的数据
 */
void TcpClient::onReadyRead() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) {
        return;
    }

    // 获取实验台号
    int benchId = socket->property("benchId").toInt();
    if (benchId == 0) {
        qWarning() << "Socket missing benchId property";
        return;
    }

    QMutexLocker locker(&m_mutex);

    auto it = m_benchConnections.find(benchId);
    if (it == m_benchConnections.end()) {
        qWarning() << "Connection not found for bench" << benchId;
        return;
    }

    BenchConnection* conn = it.value();
    if (!conn) {
        return;
    }

    // 将新接收的数据追加到缓冲区
    conn->receiveBuffer.append(socket->readAll());

    // 循环处理缓冲区中的完整数据包
    while (conn->receiveBuffer.size() >= 4) { // 至少需要读取4字节的长度字段
        // 解析头部的长度字段（包含了内容长度+4），小端字节序
        quint32 packetSize = 0;
        memcpy(&packetSize, conn->receiveBuffer.constData(), 4);

        // 检查长度字段是否合理，避免恶意数据导致的问题
        if (packetSize < 4 || packetSize > 1024 * 1024) { // 设置一个合理的最大包大小限制
            qWarning() << "Received abnormal packet size for bench" << benchId << ":" << packetSize << ", clearing buffer";
            conn->receiveBuffer.clear();
            break;
        }

        // 检查缓冲区是否包含完整的数据包
        if (conn->receiveBuffer.size() >= packetSize) {
            // 提取完整的数据包（不包括长度字段）
            QByteArray completePacket = conn->receiveBuffer.mid(0, packetSize);

            // 从缓冲区移除已处理的数据包
            conn->receiveBuffer.remove(0, packetSize);

            // 发送完整的数据包信号
            qDebug() << "Received complete packet from bench" << benchId << "size:" << completePacket.size() << "bytes";
            emit dataReceived(completePacket);
        }
        else {
            // 数据包不完整，等待更多数据
            qDebug() << "Incomplete packet from bench" << benchId << "current buffer size:" << conn->receiveBuffer.size()
                     << "required size:" << packetSize;
            break;
        }
    }
}

/**
 * 处理连接状态改变
 */
void TcpClient::onConnected() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) {
        return;
    }

    int benchId = socket->property("benchId").toInt();
    if (benchId == 0) {
        qWarning() << "Socket missing benchId property in onConnected";
        return;
    }

    QMutexLocker locker(&m_mutex);

    auto it = m_benchConnections.find(benchId);
    if (it != m_benchConnections.end()) {
        BenchConnection* conn = it.value();
        if (conn) {
            qDebug() << "Connected to server for bench" << benchId << "IP:" << conn->serverIp << "Port:" << conn->serverPort;

            conn->reconnectAttempts = 0;
            conn->isConnecting = false;
            if (conn->reconnectTimer && conn->reconnectTimer->isActive()) {
                conn->reconnectTimer->stop();
            }
        }
    }

    emit connectionStatusChanged(true);
}

/**
 * 处理断开连接
 */
void TcpClient::onDisconnected() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) {
        return;
    }

    int benchId = socket->property("benchId").toInt();
    if (benchId == 0) {
        qWarning() << "Socket missing benchId property in onDisconnected";
        return;
    }

    QMutexLocker locker(&m_mutex);

    auto it = m_benchConnections.find(benchId);
    if (it != m_benchConnections.end()) {
        BenchConnection* conn = it.value();
        if (conn) {
            qDebug() << "Disconnected from server for bench" << benchId;

            conn->isConnecting = false;

            // 如果启用了重连，则尝试重新连接
            if (m_reconnectEnabled && conn->reconnectAttempts < m_maxReconnectAttempts) {
                conn->reconnectTimer->start(m_reconnectInterval);
                qDebug() << "Will retry connection for bench" << benchId << "in" << m_reconnectInterval / 1000 << "seconds";
            }
        }
    }

    emit connectionStatusChanged(false);
}

/**
 * 处理连接错误
 */
void TcpClient::onError(QAbstractSocket::SocketError socketError) {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) {
        return;
    }

    int benchId = socket->property("benchId").toInt();
    QString errorMsg = socket->errorString();

    if (benchId > 0) {
        qWarning() << "TCP connection error for bench" << benchId << ":" << errorMsg << "error code:" << socketError;
    } else {
        qWarning() << "TCP connection error:" << errorMsg << "error code:" << socketError;
    }

    emit connectionError(errorMsg);

    // 某些错误可能需要特殊处理
    if (socketError != QAbstractSocket::RemoteHostClosedError) {
        // 远程主机关闭不是真正的错误，不需要特殊处理
    }
}

/**
 * 尝试重新连接
 */
void TcpClient::tryReconnect() {
    QTimer* timer = qobject_cast<QTimer*>(sender());
    if (!timer) {
        return;
    }

    int benchId = timer->property("benchId").toInt();
    if (benchId == 0) {
        qWarning() << "Timer missing benchId property in tryReconnect";
        return;
    }

    QMutexLocker locker(&m_mutex);

    auto it = m_benchConnections.find(benchId);
    if (it == m_benchConnections.end()) {
        qWarning() << "Connection not found for bench" << benchId << "in tryReconnect";
        return;
    }

    BenchConnection* conn = it.value();
    if (!conn || !conn->socket) {
        return;
    }

    if (conn->socket->state() == QAbstractSocket::ConnectedState) {
        qDebug() << "Already connected to server for bench" << benchId << ", canceling reconnect";
        conn->reconnectTimer->stop();
        return;
    }

    conn->reconnectAttempts++;
    qDebug() << "Attempting to reconnect for bench" << benchId << "attempt" << conn->reconnectAttempts
             << "of" << m_maxReconnectAttempts;

    if (conn->reconnectAttempts > m_maxReconnectAttempts) {
        qWarning() << "Maximum reconnect attempts reached for bench" << benchId << ", stopping reconnect";
        conn->reconnectTimer->stop();
        return;
    }

    // 重新尝试连接
    conn->socket->connectToHost(conn->serverIp, conn->serverPort);
}

/**
 * 从配置文件重新加载 IP 和端口
 */
void TcpClient::reloadFromConfig() {
    // 确保配置文件存在
    ensureConfigExists();

    loadConfig(m_currentBenchId);
}

/**
 * 设置服务器 IP 地址
 */
void TcpClient::setServerIp(const QString& ip) {
    QMutexLocker locker(&m_mutex);
    m_serverIp = ip;
    saveConfig();
}

/**
 * 设置服务器端口
 */
void TcpClient::setServerPort(quint16 port) {
    QMutexLocker locker(&m_mutex);
    m_serverPort = port;
    saveConfig();
}

/**
 * 获取服务器 IP 地址
 */
QString TcpClient::getServerIp() const {
    QMutexLocker locker(&m_mutex);
    return m_serverIp;
}

/**
 * 获取服务器端口
 */
quint16 TcpClient::getServerPort() const {
    QMutexLocker locker(&m_mutex);
    return m_serverPort;
}

/**
 * 检查是否已连接
 */
//bool TcpClient::isConnected() const {
//    QMutexLocker locker(&m_mutex);
//    return m_socket->state() == QAbstractSocket::ConnectedState;
//}

/**
 * 加载配置
 */
void TcpClient::loadConfig(int benchId) {
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");

    // 从配置文件中读取 TCP 配置
    QSettings settings(configFile, QSettings::IniFormat);

    QString srvIp = "TCP/serverIp" + QString::number(benchId);
    QString srvPort = "TCP/serverPort" + QString::number(benchId);
    // 读取服务器 IP 和端口
    m_serverIp = settings.value(srvIp, "127.0.0.1").toString();
    m_serverPort = settings.value(srvPort, 8080).toUInt();

    // 读取重连配置
    m_reconnectEnabled = settings.value("TCP/reconnectEnabled", true).toBool();
    m_reconnectInterval = settings.value("TCP/reconnectInterval", 5000).toInt();
    m_maxReconnectAttempts = settings.value("TCP/maxReconnectAttempts", 5).toInt();

    qDebug() << "从配置文件加载 TCP 配置 - IP:" << m_serverIp 
             << "端口:" << m_serverPort 
             << "重连:" << (m_reconnectEnabled ? "启用" : "禁用") 
             << "间隔:" << m_reconnectInterval 
             << "最大尝试次数:" << m_maxReconnectAttempts;
}

/**
 * 保存配置
 */
void TcpClient::saveConfig() {
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QString configFile = QDir(configPath).filePath("MicroserviceMonitor.conf");

    // 保存 TCP 配置到配置文件
    QSettings settings(configFile, QSettings::IniFormat);

    // 保存服务器 IP 和端口
    settings.setValue("TCP/serverIp", m_serverIp);
    settings.setValue("TCP/serverPort", m_serverPort);

    // 保存重连配置
    settings.setValue("TCP/reconnectEnabled", m_reconnectEnabled);
    settings.setValue("TCP/reconnectInterval", m_reconnectInterval);
    settings.setValue("TCP/maxReconnectAttempts", m_maxReconnectAttempts);

    settings.sync();

    qDebug() << "TCP 配置已保存到配置文件";
}

/**
 * 确保配置文件存在
 */
void TcpClient::ensureConfigExists() {
    // 获取配置文件路径
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir dir(configPath);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    QString configFile = dir.filePath("MicroserviceMonitor.conf");

    // 检查配置文件是否存在
    QFile file(configFile);
    if (!file.exists()) {
        qDebug() << "配置文件不存在，创建默认配置文件";
        
        // 创建默认配置文件
        QSettings settings(configFile, QSettings::IniFormat);
        
        // 添加默认的 TCP 配置段
        settings.setValue("TCP/serverIp", "127.0.0.1");
        settings.setValue("TCP/serverPort", 8080);
        settings.setValue("TCP/reconnectEnabled", true);
        settings.setValue("TCP/reconnectInterval", 5000);
        settings.setValue("TCP/maxReconnectAttempts", 5);
        
        settings.sync();
    } else {
        // 检查是否存在 TCP 配置段，如果不存在则添加
        QSettings settings(configFile, QSettings::IniFormat);
        if (!settings.childGroups().contains("TCP")) {
            qDebug() << "TCP 配置段不存在，添加默认配置";
            
            settings.setValue("TCP/serverIp", "127.0.0.1");
            settings.setValue("TCP/serverPort", 8080);
            settings.setValue("TCP/reconnectEnabled", true);
            settings.setValue("TCP/reconnectInterval", 5000);
            settings.setValue("TCP/maxReconnectAttempts", 5);
            
            settings.sync();
        }
    }
} 

//切换实验台号（兼容旧接口）
int TcpClient::updaeteBench(int benchId)
{
    QMutexLocker locker(&m_mutex);

    int oldBenchId = m_currentBenchId;
    m_currentBenchId = benchId;

    qDebug() << "Switching from bench" << oldBenchId << "to bench" << benchId;

    // 新架构下不需要断开所有连接，只需要设置当前默认实验台号
    // 连接会在需要时自动建立

    return benchId;
}
