﻿#include "icd_view_dialog.h"
#include "ui_icd_view_dialog.h"
#include "third/parseAndAnalysis.h"
#include "utils/file_utils.h"
#include "utils/user_session.h"
#include "utils/api_url_manager.h"

#include <QFileDialog>
#include <QDateTime>
#include <QMessageBox>
#include <QStandardPaths>
#include <QMenu>
#include <QPushButton>
#include <QHeaderView>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>
#include <QKeyEvent>
#include <QStack>
#include <QDialog>
#include <QListWidgetItem>
#include <QTextStream>
#include <QTextCodec>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QJsonObject>
#include <QJsonDocument>
#include <QTimer>
#include <QNetworkReply>

ICDViewDialog::ICDViewDialog(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::ICDViewDialog)
{
    ui->setupUi(this);
    
    // 获取UI组件引用
    titleLabel = ui->titleLabel;
    addButton = ui->addButton;
    importButton = ui->importButton;
    closeButton = ui->closeButton;
    leftSeparatorLine = ui->leftSeparatorLine;
    mainSplitter = ui->mainSplitter;
    
    // 左侧组件
    searchEdit = ui->searchEdit;
    fileListWidget = ui->fileListWidget;
    leftTitleLabel = ui->leftTitleLabel;
    
    // 右侧组件
    contentLabel = ui->contentLabel;
    contentTreeView = ui->contentTreeView;
    saveButton = ui->saveButton;
    
    // 创建并设置文本编辑组件
    contentTextEdit = new QTextEdit(this);
    contentTextEdit->setReadOnly(false);  // 允许编辑
    contentTextEdit->setFont(QFont("Courier New", 10));  // 使用等宽字体
    contentTextEdit->setLineWrapMode(QTextEdit::NoWrap);  // 不自动换行
    
    // 替换树形视图为文本编辑组件
    QLayout* rightLayout = ui->rightWidget->layout();
    if (rightLayout) {
        // 移除原树形视图
        rightLayout->removeWidget(contentTreeView);
        contentTreeView->hide();
        
        // 在对应位置添加文本编辑组件
        QVBoxLayout* vLayout = qobject_cast<QVBoxLayout*>(rightLayout);
        if (vLayout) {
            vLayout->insertWidget(1, contentTextEdit);
        }
    }
    
    // 初始化网络管理器
    networkManager = new QNetworkAccessManager(this);
    connect(networkManager, &QNetworkAccessManager::finished, this, &ICDViewDialog::onUploadFinished);
    
    setupUI();
    setupConnections();
    loadICDFileList();
}

ICDViewDialog::~ICDViewDialog()
{
    delete ui;
}

void ICDViewDialog::setupUI()
{
    // 获取UI中的控件
    titleLabel = ui->titleLabel;
    addButton = ui->addButton;
    importButton = ui->importButton;
    closeButton = ui->closeButton;
    leftSeparatorLine = ui->leftSeparatorLine;
    mainSplitter = ui->mainSplitter;
    
    leftTitleLabel = ui->leftTitleLabel;
    searchEdit = ui->searchEdit;
    fileListWidget = ui->fileListWidget;
    
    contentLabel = ui->contentLabel;
    saveButton = ui->saveButton;
    
    // 设置文本编辑器
    contentTextEdit->setStyleSheet("QTextEdit { border: 1px solid #e8e8e8; border-radius: 4px; background-color: white; }");
    
    // 设置标题文本
    titleLabel->setText("ICD文件管理");
    leftTitleLabel->setText("ICD文件列表");
    contentLabel->setText("文件内容");
    
    // 设置搜索提示
    searchEdit->setPlaceholderText("搜索文件名...");
    
    // 取消左侧列表显示边框
    fileListWidget->setFrameShape(QFrame::NoFrame);
    
    // 初始化文件列表
    loadICDFileList();
}

void ICDViewDialog::setupConnections()
{
    // 连接信号和槽
    connect(searchEdit, &QLineEdit::textChanged, this, &ICDViewDialog::onSearchTextChanged);
    connect(addButton, &QPushButton::clicked, this, &ICDViewDialog::onAddFileClicked);
    connect(importButton, &QPushButton::clicked, this, &ICDViewDialog::onImportFileClicked);
    connect(closeButton, &QPushButton::clicked, this, &ICDViewDialog::onCloseButtonClicked);
    connect(saveButton, &QPushButton::clicked, this, &ICDViewDialog::onSaveButtonClicked);
    
    // 列表项双击查看文件
    connect(fileListWidget, &QListWidget::itemDoubleClicked, 
            [this](QListWidgetItem *item) {
                if (item) {
                    QString filePath = item->data(Qt::UserRole).toString();
                    this->onViewFile(filePath);
                }
            });
            
    // 列表项右键菜单
    fileListWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(fileListWidget, &QListWidget::customContextMenuRequested,
            [this](const QPoint &pos) {
                QListWidgetItem *item = fileListWidget->itemAt(pos);
                if (item) {
                    QString filePath = item->data(Qt::UserRole).toString();
                    
                    QMenu contextMenu(this);
                    
                    QAction *viewAction = new QAction(tr("查看"), &contextMenu);
                    connect(viewAction, &QAction::triggered, [this, filePath]() {
                        this->onViewFile(filePath);
                    });
                    contextMenu.addAction(viewAction);
                    
                    QAction *deleteAction = new QAction(tr("删除"), &contextMenu);
                    connect(deleteAction, &QAction::triggered, [this, filePath]() {
                        this->onDeleteFile(filePath);
                    });
                    contextMenu.addAction(deleteAction);
                    
                    contextMenu.exec(fileListWidget->mapToGlobal(pos));
                }
            });
}

void ICDViewDialog::loadICDFileList()
{
    // 清除当前列表
    fileListWidget->clear();
    icdFilePaths.clear();
    
    // 获取ICD文件保存路径
    // QString icdPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/icd_files";
    // 单签当前执行文件的目录
    QString icdPath = QCoreApplication::applicationDirPath() + "/icd_files";

    // 确保目录存在
    FileUtils::ensureDirectoryExists(icdPath);
    
    // 读取目录中的所有文件
    QDir dir(icdPath);
    QStringList fileFilters;
    fileFilters << "*.icd" << "*.xml";
    QFileInfoList fileList = dir.entryInfoList(fileFilters, QDir::Files, QDir::Time);
    
    // 添加所有文件到列表
    for (const QFileInfo &fileInfo : fileList) {
        addFileItem(fileInfo.absoluteFilePath());
    }
}

void ICDViewDialog::addFileItem(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    
    // 创建列表项
    QListWidgetItem *item = new QListWidgetItem(fileListWidget);
    
    // 设置文件图标
    item->setIcon(QIcon(":/images/file_xml.png"));
    
    // 设置文件名
    item->setText(fileInfo.fileName());
    
    // 设置提示信息（包含文件信息）
    QString lastModified = fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss");
    
    // 计算文件大小
    double fileSize = fileInfo.size();
    QString sizeText;
    if (fileSize < 1024) {
        sizeText = QString::number(fileSize) + " B";
    } else if (fileSize < 1024 * 1024) {
        sizeText = QString::number(fileSize / 1024, 'f', 2) + " KB";
    } else {
        sizeText = QString::number(fileSize / (1024 * 1024), 'f', 2) + " MB";
    }
    
    // 设置提示信息
    item->setToolTip(tr("文件名: %1\n修改日期: %2\n大小: %3")
                    .arg(fileInfo.fileName())
                    .arg(lastModified)
                    .arg(sizeText));
    
    // 保存文件路径
    item->setData(Qt::UserRole, filePath);
    icdFilePaths.append(filePath);
}

void ICDViewDialog::filterFileList(const QString &text)
{
    // 如果搜索文本为空，显示所有文件
    if (text.isEmpty()) {
        for (int i = 0; i < fileListWidget->count(); ++i) {
            fileListWidget->item(i)->setHidden(false);
        }
        return;
    }
    
    // 否则过滤显示匹配的文件
    for (int i = 0; i < fileListWidget->count(); ++i) {
        QListWidgetItem *item = fileListWidget->item(i);
        bool match = item->text().contains(text, Qt::CaseInsensitive);
        item->setHidden(!match);
    }
}

void ICDViewDialog::onSearchTextChanged(const QString &text)
{
    filterFileList(text);
}

void ICDViewDialog::onViewFile(const QString &filePath)
{
    // 保存当前文件路径
    currentFilePath = filePath;
    
    // 显示文件内容
    showFileContent(filePath);
    
    // 确保分割器合理分配空间
    QList<int> sizes = mainSplitter->sizes();
    if (sizes[1] < 100) {  // 如果右侧区域太小
        int totalWidth = mainSplitter->width();
        sizes[0] = totalWidth * 0.4;  // 左侧占40%
        sizes[1] = totalWidth * 0.6;  // 右侧占60%
        mainSplitter->setSizes(sizes);
    }
}

void ICDViewDialog::showFileContent(const QString &filePath)
{
    // 更新内容标签
    QFileInfo fileInfo(filePath);
    contentLabel->setText(tr("文件内容: %1").arg(fileInfo.fileName()));
    
    // 读取XML文件内容
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("文件打开错误"), tr("无法打开文件: %1").arg(file.errorString()));
        return;
    }
    
    // 读取所有文本内容
    QTextStream in(&file);
    in.setCodec("UTF-8");  // 设置编码为UTF-8
    QString fileContent = in.readAll();
    file.close();
    
    // 在文本编辑器中显示XML内容
    contentTextEdit->setPlainText(fileContent);
}

void ICDViewDialog::onDeleteFile(const QString &filePath)
{
    // 确认删除
    QMessageBox::StandardButton reply = QMessageBox::question(this, tr("确认删除"), 
                                                             tr("确定要删除文件 %1 吗?").arg(QFileInfo(filePath).fileName()),
                                                             QMessageBox::Yes | QMessageBox::No);
    if (reply != QMessageBox::Yes) {
        return;
    }
    
    // 删除文件
    QFile file(filePath);
    if (file.remove()) {
        // 从列表中移除
        for (int i = 0; i < fileListWidget->count(); ++i) {
            QListWidgetItem *item = fileListWidget->item(i);
            if (item->data(Qt::UserRole).toString() == filePath) {
                delete fileListWidget->takeItem(i);
                break;
            }
        }
        
        // 从路径列表中移除
        icdFilePaths.removeAll(filePath);
        
        // 如果删除的是当前正在显示的文件，则清空内容
        if (filePath == currentFilePath) {
            contentTextEdit->clear();
            currentFilePath.clear();
            contentLabel->setText(tr("文件内容"));
        }
        
        QMessageBox::information(this, tr("删除成功"), tr("文件已成功删除"));
        
        // 发射文件列表更新信号，通知其他组件刷新
        emit fileListUpdated();
    } else {
        QMessageBox::warning(this, tr("删除失败"), tr("无法删除文件: %1").arg(file.errorString()));
    }
}

void ICDViewDialog::onAddFileClicked()
{
    // 此方法可以用于创建新的ICD文件
    QMessageBox::information(this, tr("新建ICD文件"), tr("新建ICD文件功能尚未实现"));
}

void ICDViewDialog::onImportFileClicked()
{
    // 选择要导入的文件
    QStringList fileNames = QFileDialog::getOpenFileNames(this,
        tr("导入ICD文件"), QDir::homePath(),
        tr("ICD文件 (*.icd);;XML文件 (*.xml);;所有文件 (*)"));
        
    if (fileNames.isEmpty()) {
        return;
    }
    
    // 获取ICD文件保存路径 - 修复路径不一致问题
    QString icdPath = QCoreApplication::applicationDirPath() + "/icd_files";
    
    // 确保目录存在
    FileUtils::ensureDirectoryExists(icdPath);
    
    // 导入所有选中的文件
    int successCount = 0;
    for (const QString &fileName : fileNames) {
        QFileInfo srcFileInfo(fileName);
        QString destFilePath = QDir(icdPath).filePath(srcFileInfo.fileName());
        
        // 检查是否已存在同名文件
        QFileInfo destFileInfo(destFilePath);
        if (destFileInfo.exists()) {
            QMessageBox::StandardButton reply = QMessageBox::question(this, tr("文件已存在"), 
                                                                     tr("文件 %1 已存在，是否覆盖?").arg(srcFileInfo.fileName()),
                                                                     QMessageBox::Yes | QMessageBox::No);
            if (reply != QMessageBox::Yes) {
                continue;
            }
            
            // 删除已存在的文件
            QFile existingFile(destFilePath);
            existingFile.remove();
        }
        
        // 复制文件
        if (QFile::copy(fileName, destFilePath)) {
            successCount++;
            
            // 添加到文件列表
            addFileItem(destFilePath);
        }
    }
    
    // 显示导入结果
    if (successCount > 0) {
        QMessageBox::information(this, tr("导入成功"), 
                                tr("成功导入 %1 个文件").arg(successCount));
        
        // 发射文件列表更新信号，通知其他组件刷新
        emit fileListUpdated();
    } else {
        QMessageBox::warning(this, tr("导入失败"), tr("没有成功导入任何文件"));
    }
}

void ICDViewDialog::onCloseButtonClicked()
{
    // 关闭对话框
    if (QDialog *parentDialog = qobject_cast<QDialog*>(parent())) {
        parentDialog->close();
    }
}

void ICDViewDialog::onSaveButtonClicked()
{
    // 如果没有选择文件，提示用户
    if (currentFilePath.isEmpty()) {
        QMessageBox::warning(this, tr("未选择文件"), tr("请先选择一个ICD文件"));
        return;
    }
    
    // 将修改后的内容保存到文件
    if (saveXmlFile(currentFilePath)) {
        QMessageBox::information(this, tr("保存成功"), tr("文件 %1 已成功保存").arg(QFileInfo(currentFilePath).fileName()));
        
        // 获取文件内容和文件名
        QString content = contentTextEdit->toPlainText();
        QString fileName = QFileInfo(currentFilePath).fileName();
        
        // 上传到服务器
        uploadToServer(content, fileName);
    } else {
        QMessageBox::warning(this, tr("保存失败"), tr("保存文件 %1 时发生错误").arg(QFileInfo(currentFilePath).fileName()));
    }
}

bool ICDViewDialog::saveXmlFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("文件保存错误"), tr("无法打开文件进行写入: %1").arg(file.errorString()));
        return false;
    }
    
    // 获取文本编辑器中的内容
    QString content = contentTextEdit->toPlainText();
    
    // 写入文件
    QTextStream out(&file);
    out.setCodec("UTF-8");  // 设置编码为UTF-8
    out << content;
    
    file.close();
    return true;
}

QString ICDViewDialog::extractVersionNumberXml(const QString &input) {
    QXmlStreamReader reader(input);

    while (!reader.atEnd()) {
        QXmlStreamReader::TokenType token = reader.readNext();

        if (token == QXmlStreamReader::StartElement &&
            reader.name() == "版本编号") {
            return reader.readElementText();
        }
    }

    return QString();
}

void ICDViewDialog::uploadToServer(const QString &content, const QString &fileName)
{
    // 显示正在上传的提示
    // QMessageBox msgBox;
    // msgBox.setWindowTitle(tr("正在上传"));
    // msgBox.setText(tr("正在将文件 %1 上传到服务器...").arg(fileName));
    // msgBox.setStandardButtons(QMessageBox::NoButton);
    // msgBox.setIcon(QMessageBox::Information);
    
    // // 创建异步消息框，允许继续执行后续代码
    // QTimer::singleShot(100, &msgBox, &QMessageBox::close);
    // msgBox.show();
    
    // 获取用户会话数据
    int ret;
    QString token = UserSession::getInstance().getToken();
    QString icdPath = QCoreApplication::applicationDirPath() + "/icd_files";
    // QString icdFilePath = icdPath + "/1.xml";
    QString icdFilePath = icdPath + "/" + fileName;
    std::string icdVersion;
    ret = loadIcd(icdFilePath.toStdString());
//    ret = parseIcdAndGenerateDataFile( icdFilePath.toStdString(), icdPath.toStdString(), icdVersion);
    

    // 准备请求
    QNetworkRequest request(ApiUrlManager::getInstance().getUrl(ApiUrlManager::FILE_UPLOAD));
    request.setRawHeader("accept", "*/*");
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }
    
    // 创建 JSON 数据
    QJsonObject jsonObj;
    jsonObj["content"] = content;
    // jsonObj["id"] = 0;  // 按接口要求设置为0
    jsonObj["name"] = fileName;
    jsonObj["type"] = "icd";
    jsonObj["icdVersion"] =  extractVersionNumberXml(content);
    
    // 添加用户ID（如果可用）
    if (UserSession::getInstance().isLoggedIn() && UserSession::getInstance().getUserId() > 0) {
        jsonObj["userId"] = UserSession::getInstance().getUserId();
    }
    
    QJsonDocument jsonDoc(jsonObj);
    QByteArray jsonData = jsonDoc.toJson();
    
    // 发送 POST 请求
    networkManager->post(request, jsonData);
}

void ICDViewDialog::onUploadFinished(QNetworkReply *reply)
{
    // 处理响应
    if (reply->error() == QNetworkReply::NoError) {
        // 读取响应数据
        QByteArray responseData = reply->readAll();
        
        // 尝试解析 JSON 响应
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
        if (!jsonDoc.isNull() && jsonDoc.isObject()) {
            QJsonObject responseObj = jsonDoc.object();
            
            // 获取顶层字段
            QString code = responseObj["code"].toString();
            QString message = responseObj["message"].toString();
            bool success = responseObj["success"].toBool();
            
            if (success) {
                // 获取 data 对象
                QJsonObject dataObj = responseObj["data"].toObject();
                if (!dataObj.isEmpty()) {
                    // 提取 data 中的字段
                    QString name = dataObj["name"].toString();
                    QString type = dataObj["type"].toString();
                    QString url = dataObj["url"].toString();
                    QString icdVersion = dataObj["icdVersion"].toString();
                    QString digitalModel = dataObj["digitalModel"].toString();
                    QString createTime = dataObj["createTime"].toString();
                    QString updateTime = dataObj["updateTime"].toString();
                    int id = dataObj["id"].toInt();
                    
                    // 构建成功消息
                    QString successMsg = tr("文件已成功上传到服务器\n\n");
                    successMsg += tr("名称: %1\n").arg(name);
                    successMsg += tr("类型: %1\n").arg(type);
                    
                    if (!icdVersion.isEmpty())
                        successMsg += tr("ICD版本: %1\n").arg(icdVersion);
                    
                    if (!digitalModel.isEmpty())
                        successMsg += tr("数字模型: %1\n").arg(digitalModel);
                    
                    successMsg += tr("ID: %1\n").arg(id);
                    successMsg += tr("创建时间: %1\n").arg(createTime);
                    successMsg += tr("更新时间: %1\n").arg(updateTime);
                    
                    if (!url.isEmpty())
                        successMsg += tr("URL: %1").arg(url);
                    
                    QMessageBox::information(this, tr("上传成功"), successMsg);
                } else {
                    QMessageBox::information(this, tr("上传成功"), 
                                            tr("文件已成功上传，但服务器返回的数据为空"));
                }
                
                // 上传成功后刷新文件列表
                loadICDFileList();
                
                // 发射文件列表更新信号，通知其他组件刷新
                emit fileListUpdated();
            } else {
                // 显示错误信息
                QString errorMsg = tr("上传失败: %1").arg(message);
                QMessageBox::warning(this, tr("上传失败"), errorMsg);
            }
        } else {
            QMessageBox::information(this, tr("上传成功"), 
                                    tr("文件已上传，但服务器响应不是有效的JSON格式"));
        }
    } else {
        // 显示网络错误信息
        QMessageBox::warning(this, tr("上传失败"), 
                            tr("无法上传文件: %1").arg(reply->errorString()));
    }
    
    // 释放响应对象
    reply->deleteLater();
}
