#include <QDebug>
#include <QMessageBox>
#include <QFileDialog>
#include <QHeaderView>
#include <QDateTime>
#include <QApplication>
#include <QSplitter>
#include <QGroupBox>
#include <QFileInfo>
#include <QScrollBar>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>

#include "fc_monitor_widget.h"
#include "ui_fc_monitor_widget.h"
#include "../scheme/scheme_list_item.h"
#include "../../utils/user_session.h"
#include "utils/response_data_store.h"
#include "utils/api_url_manager.h"
#include "utils/tcp_client.h"
#include "utils/unit_model.h"
#include "unit_standard_item.h"


FcMonitorWidget::FcMonitorWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::FcMonitorWidget),
    m_dataManager(new FcDataManager(this)),
    m_dataParser(new FcDataParser(this)),
    m_isMonitoring(true),
    m_isPaused(false),
    m_updateTimer(new QTimer(this)),
    m_totalMessages(0),
    m_activeMessages(0),
    m_totalTopics(0),
    m_activeTopics(0),
    addSchemeDialog(nullptr),
    m_unitDelegate(new UnitComboDelegate(this)),
    m_isExpanded(true),  // 默认展开状态
    m_showHeader(false), // 默认不显示帧头
    m_currentBenchId(1)  // 默认实验台号
{
    ui->setupUi(this);

    setupConnections();

    // 初始化按钮文案
    ui->layoutButton->setText(m_isExpanded ? "收缩" : "展开");
    ui->showHeaderButton->setText(m_showHeader ? "隐藏帧头" : "显示帧头");

    // 设置更新定时器
    m_updateTimer->setInterval(UPDATE_INTERVAL);
    connect(m_updateTimer, &QTimer::timeout, this, &FcMonitorWidget::onUpdateTimer);
    m_updateTimer->start();

    // 注册到订阅规则管理器
    SubRuleManager::getInstance().registerWidget(this, this);

    qDebug() << "FC monitor widget initialization completed";
    initSchemeList();
    initTimeList();
}

FcMonitorWidget::~FcMonitorWidget()
{
    // 停止监控
    if (m_isMonitoring) {
        stopMonitoring(m_currentSchemeName);
    }

    // 从订阅规则管理器注销
    SubRuleManager::getInstance().unregisterWidget(this);

    // 清理资源
    clearOldData();
}

//解析方案xml获取方案信息
void FcMonitorWidget::initSchemeList()
{
    // 获取用户名和方案文件路径
    QString userId =  QString::number(UserSession::getInstance().getUserId());
    QString fileName = UserSession::getInstance().getUsername() + "_solution.xml" ;
    QString filePath =  QCoreApplication::applicationDirPath() + "/scheme/" + fileName;


    // // 下载并保存XML文件
    downloadAndSaveXmlFile(userId, filePath,[this, fileName, filePath](bool success) {
        if (success) {
            // 下载成功后解析XML
            qWarning() << "parser XML file success:" << filePath;

            // 只有当downloadAndSaveXmlFile成功且m_loadXmlUrl不为空时才执行downloadAndSaveFile
            if (!m_loadXmlUrl.isEmpty()) {
                downloadAndSaveFile(fileName, filePath, [this,filePath](bool success) {
                    if(success) {
                        // 下载成功后解析XML
                        // 使用数据管理器解析XML
                        if ( m_dataManager) {
                            // 从文件名提取方案名称
                            QFileInfo fileInfo(filePath);
                            if (m_dataManager->parseXmlData(filePath)) {
                                qDebug() << "Successfully parsed FC scheme:" ;
                                // 更新方案列表
                                updateSchemeList();
                            } else {
                                qDebug() << "Failed to parse FC scheme:" ;
                            }
                        }
                        //qDebug() << "Successfully downloaded scheme from minio";
                    }
                    else{
                        qDebug() << "Failed to download scheme from minio";
                    }
                });
            }
        } else {
            qWarning() << "Failed to download XML file" ;
        }
    });
}


void FcMonitorWidget::initTimeList()
{
    // 创建时间列表控件
     timeListWidget = new QListWidget(this);
     timeListWidget->setStyleSheet(
         "QListWidget {"
         "   border: 1px solid #d9d9d9;"
         "   border-radius: 4px;"
         "   background-color: white;"
         "}"
         "QListWidget::item {"
         "   padding: 8px;"
         "   border-bottom: 1px solid #f0f0f0;"
         "}"
         "QListWidget::item:hover {"
         "   background-color: #e6f7ff;"
         "}"
         "QListWidget::item:selected {"
         "   background-color: #bae7ff;"
         "   color: black;"
         "}"
     );

     // 设置固定宽度为200px
     timeListWidget->setFixedWidth(200);

     // 将时间列表添加到中间树右侧面板
     QHBoxLayout* boxLayout = ui->horizontalLayout_2;
     boxLayout->addWidget(timeListWidget);
     connect(timeListWidget, SIGNAL(currentRowChanged(int)), this, SLOT(TimeListWidgetRowChanged(int)));
     timeListWidget->setFixedWidth(200);
     timeListWidget->hide();
}


void FcMonitorWidget::onTemplateDeleted(const QString& templateName)
{
    // 在方案列表中查找并更新对应方案的分享状态
    // 遍历所有方案项
    // 遍历schemeListWidget上的所有项目
    for (int i = 0; i < ui->schemeListWidget->count(); i++) {
        QListWidgetItem* listItem = ui->schemeListWidget->item(i);
        if (listItem) {
            SchemeListItem* schemeItem = qobject_cast<SchemeListItem*>(ui->schemeListWidget->itemWidget(listItem));
            if (schemeItem && schemeItem->getSchemeName() == templateName) {
                // 更新分享按钮状态
                //schemeItem->updateShareStatus(isShared);
                schemeItem->setShareStatus(false);
                break;
            }
        }
    }
}

void FcMonitorWidget::setFrameHeaderVisibility(QTreeView* treeView, QStandardItemModel* model, bool hidden)
{
    if (!treeView || !model) {
        return;
    }

    // 遍历所有功能单元消息节点
    for (int i = 0; i < model->rowCount(); ++i) {
        QStandardItem* messageItem = model->item(i, 0);
        if (messageItem) {
            // 遍历消息节点的子节点，查找"帧头数据"节点
            for (int j = 0; j < messageItem->rowCount(); ++j) {
                QStandardItem* childItem = messageItem->child(j, 0);
                if (childItem && childItem->text() == "帧头数据") {
                    // 找到帧头数据节点，设置其可见性
                    QModelIndex headerIndex = childItem->index();
                    treeView->setRowHidden(headerIndex.row(), headerIndex.parent(), hidden);
                    break; // 每个消息节点只有一个帧头数据节点
                }
            }
        }
    }
}

void FcMonitorWidget::onShowHeaderButtonClicked()
{
    // 切换帧头显示状态
    m_showHeader = !m_showHeader;
    
    // 更新按钮文本
    ui->showHeaderButton->setText(m_showHeader ? "隐藏帧头" : "显示帧头");
    
    // 如果当前有活跃的方案，控制帧头节点的可见性
    if (!m_currentSchemeName.isEmpty()) {
        if (m_schemeModels.contains(m_currentSchemeName) && m_schemeTreeViews.contains(m_currentSchemeName)) {
            QStandardItemModel* model = m_schemeModels[m_currentSchemeName];
            QTreeView* treeView = m_schemeTreeViews[m_currentSchemeName];
            
            if (model && treeView) {
                // 遍历所有功能单元消息节点
                for (int i = 0; i < model->rowCount(); ++i) {
                    QStandardItem* messageItem = model->item(i, 0);
                    if (messageItem) {
                        // 查找帧头数据节点（通常是第一个子节点）
                        for (int j = 0; j < messageItem->rowCount(); ++j) {
                            QStandardItem* childItem = messageItem->child(j, 0);
                            if (childItem && childItem->text() == "帧头数据") {
                                // 控制帧头节点的可见性
                                QModelIndex headerIndex = childItem->index();
                                treeView->setRowHidden(headerIndex.row(), headerIndex.parent(), !m_showHeader);
                                break;
                            }
                        }
                    }
                }
            }
        }
        
        qDebug() << "Frame header display toggled:" << (m_showHeader ? "shown" : "hidden");
    }
}

void FcMonitorWidget::onTemplateDownload()
{
    //这里需要重新下载并解析xml生成方案
    //有下载才去更新
    //loadScheme();
    m_isDownload=true;
}

void FcMonitorWidget::setupConnections()
{
    // 数据管理器信号连接
    connect(m_dataManager, &FcDataManager::schemeDataChanged,
            this, &FcMonitorWidget::onSchemeDataChanged);
    connect(m_dataManager, &FcDataManager::schemeAdded,
            this, &FcMonitorWidget::onSchemeAdded);
    connect(m_dataManager, &FcDataManager::messageDataUpdated,
            this, &FcMonitorWidget::onMessageDataUpdated);
    connect(m_dataManager, &FcDataManager::topicDataUpdated,
            this, &FcMonitorWidget::onTopicDataUpdated);

    // 数据解析器信号连接
    connect(m_dataParser, &FcDataParser::parseError,
            this, &FcMonitorWidget::onParseError);

    // 界面组件信号连接
    connect(ui->schemeListWidget, &QListWidget::itemClicked,
            this, &FcMonitorWidget::onSchemeItemClicked);

    connect(ui->schemeSearchEdit, &QLineEdit::textChanged,
            this, &FcMonitorWidget::onSearchTextChanged);

    // 工具栏按钮连接
    connect(ui->schemeAddButton, &QPushButton::clicked,
            this, &FcMonitorWidget::onAddSchemeClicked);
    connect(ui->pauseButton, &QPushButton::clicked,
            this, &FcMonitorWidget::onPauseClicked);
    connect(ui->clearButton, &QPushButton::clicked,
            this, &FcMonitorWidget::onClearDataClicked);
    connect(ui->exportButton, &QPushButton::clicked,
            this, &FcMonitorWidget::onExportDataClicked);
    connect(ui->splitScreen, &QPushButton::clicked,
            this, &FcMonitorWidget::onSplitScreenClicked);

    // 方案标签页相关连接
    connect(ui->dataTabWidget, &QTabWidget::tabCloseRequested, this, &FcMonitorWidget::onDataTabClosed);
    connect(ui->dataTabWidget, &QTabWidget::currentChanged, this, &FcMonitorWidget::onTabChanged);

    connect(ui->templateListWgt, &TemplateListWidget::templateDeleted,
               this, &FcMonitorWidget::onTemplateDeleted);
    connect(ui->templateListWgt,&TemplateListWidget::templateDownload,
               this,&FcMonitorWidget::onTemplateDownload);

    //模板和方案列表切换
    connect(ui->tabWidget, &QTabWidget::currentChanged, this, &FcMonitorWidget::onSchemeAndTemplateTabChanged);

    // 单位切换委托信号连接
//    connect(m_unitDelegate, &UnitComboDelegate::unitChanged,
//            this, &FcMonitorWidget::onUnitChanged);

    //展开和收缩功能绑定
    connect(ui->layoutButton, &QPushButton::clicked, this, &FcMonitorWidget::onLayoutButtonClicked);
    
    //显示/隐藏帧头功能绑定
    connect(ui->showHeaderButton, &QPushButton::clicked, this, &FcMonitorWidget::onShowHeaderButtonClicked);
}

void FcMonitorWidget::loadScheme()
{
    QString fileName = UserSession::getInstance().getUsername() + "_solution.xml" ;
    QString filePath =  QCoreApplication::applicationDirPath() + "/scheme/" + fileName;

    if (m_dataManager->loadSchemeFromFile(filePath)) {
        updateSchemeList();
        //loadSchemeDataView(schemeName);
    } else {
        QMessageBox::warning(this, "错误", QString("加载方案失败: %1").arg(fileName));
    }
}

void FcMonitorWidget::addScheme(const FcSchemeData& scheme)
{
    m_dataManager->addScheme(scheme);
    updateSchemeList();
    loadSchemeDataView(scheme.schemeName);
}

void FcMonitorWidget::removeScheme(const QString& schemeName)
{
    if (m_currentSchemeName == schemeName && m_isMonitoring) {
        stopMonitoring(schemeName);
    }

    cleanupResources(schemeName);
    m_dataManager->removeScheme(schemeName);
    updateSchemeList();

    // 如果删除的是当前方案，清空右侧显示
    if (m_currentSchemeName == schemeName) {
        m_currentSchemeName.clear();
        ui->dataTabWidget->clear();
    }
}

QStringList FcMonitorWidget::getSchemeNames() const
{
    return m_dataManager->getSchemeNames();
}

void FcMonitorWidget::startMonitoring(const QString& schemeName)
{
    if (schemeName.isEmpty()) {
        QMessageBox::warning(this, "警告", "请先选择一个监控方案");
        return;
    }

    m_dataManager->startMonitoring(schemeName);
    m_isMonitoring = true;
    m_isPaused = false;
    m_currentSchemeName = schemeName;
    m_startTime = QDateTime::currentDateTime();

    // 启动更新定时器
    m_updateTimer->start();

    updateMonitoringState();
    emit monitoringStateChanged(true);

    qDebug() << "Started monitoring FC scheme:" << schemeName;
}

void FcMonitorWidget::stopMonitoring(const QString& schemeName)
{
    m_dataManager->stopMonitoring(schemeName);
    m_isMonitoring = false;
    m_isPaused = false;

    // 停止更新定时器
    m_updateTimer->stop();

    updateMonitoringState();
    emit monitoringStateChanged(false);

    qDebug() << "Stopped monitoring FC scheme:" << schemeName;
}

void FcMonitorWidget::pauseMonitoring()
{
    m_isPaused = !m_isPaused;
    if(m_isPaused)
    {
        m_updateTimer->stop();
    }
    else {
        m_updateTimer->start();
    }
    QPushButton* button = qobject_cast<QPushButton*>(sender());

    // 恢复命令返回信息初始值
    ResponseDataStore::getInstance().setCmdReuslt(false);
    button->setText(m_isPaused ? "继 续" : "暂 停");
    button->setIcon(QIcon(QString::fromUtf8(m_isPaused ? ":/images/play.png" : ":/images/pause.png")));

    if (!m_isPaused) {
        // updateDataView();
        //隐藏历史时间列表
        timeListWidget->hide();
    }


//    if (m_isMonitoring) {
//        if (m_isPaused) {
//            m_dataManager->resumeMonitoring(m_currentSchemeName);
//            m_isPaused = false;
//            m_updateTimer->start();
//        } else {
//            m_dataManager->pauseMonitoring(m_currentSchemeName);
//            m_isPaused = true;
//            m_updateTimer->stop();
//        }
//        updateMonitoringState();
//    }
}

void FcMonitorWidget::resumeMonitoring()
{
    if (m_isMonitoring && m_isPaused) {
        pauseMonitoring(); // 切换暂停状态
    }
}

bool FcMonitorWidget::isMonitoring() const
{
    return m_isMonitoring;
}

bool FcMonitorWidget::isPaused() const
{
    return m_isPaused;
}

void FcMonitorWidget::updateData(const QByteArray& rawData)
{
    if (!m_isMonitoring || m_isPaused || rawData.isEmpty()) {
        return;
    }

    // 解析原始数据
    FcDataParser::ParsedFrameData frameData = m_dataParser->parseRawFrameData(rawData);

    if (frameData.isValid) {
        // 更新数据管理器中的数据
        m_dataManager->updateMessageData(m_currentSchemeName, frameData.messageId, frameData.topicValues);

        // 发出数据更新信号
        emit dataUpdated(m_currentSchemeName);
    }
}

void FcMonitorWidget::updateMessageData(const QString& schemeName, const QString& messageId,
                                       const QMap<QString, QString>& topicValues)
{
    m_dataManager->updateMessageData(schemeName, messageId, topicValues);
}

void FcMonitorWidget::loadSchemeDataView(const QString& schemeName)
{
    FcSchemeData* scheme = m_dataManager->getScheme(schemeName);
    if (!scheme) {
        qWarning() << "Scheme not found:" << schemeName;
        return;
    }

    // 检查是否已经存在该方案的标签页
    for (int i = 0; i < ui->dataTabWidget->count(); ++i) {
        if (ui->dataTabWidget->tabText(i) == schemeName) {
            ui->dataTabWidget->setCurrentIndex(i);
            return;
        }
    }

    // 创建新的树视图和数据模型
    QTreeView* treeView = createDataTreeView();
    QStandardItemModel* model = createDataModel(schemeName);

    // 构建树模型
    buildTreeModel(model, *scheme);

    // 设置模型到树视图
    treeView->setModel(model);
    optimizeTreeViewPerformance(treeView);

    // 连接树视图点击信号
    connect(treeView, &QTreeView::clicked, this, &FcMonitorWidget::onTreeItemClicked);

    // 保存引用
    m_schemeTreeViews[schemeName] = treeView;
    m_schemeModels[schemeName] = model;

    // 添加到标签页
    int tabIndex = ui->dataTabWidget->addTab(treeView, schemeName);
    ui->dataTabWidget->setCurrentIndex(tabIndex);

    // 展开所有节点
    treeView->expandAll();

    // 调整列宽
    treeView->resizeColumnToContents(0);
    treeView->resizeColumnToContents(1);
    
    // 设置帧头节点的初始可见性（确保默认隐藏）
    setFrameHeaderVisibility(treeView, model, !m_showHeader);

}

QTreeView* FcMonitorWidget::createDataTreeView()
{
    QTreeView* treeView = new QTreeView();
    treeView->setAlternatingRowColors(true);
    // 修改编辑触发器，添加单击触发
    treeView->setEditTriggers(QAbstractItemView::DoubleClicked |
                              QAbstractItemView::EditKeyPressed |
                              QAbstractItemView::SelectedClicked);
    treeView->setSelectionBehavior(QAbstractItemView::SelectRows);
    treeView->setSelectionMode(QAbstractItemView::SingleSelection);
    treeView->setContextMenuPolicy(Qt::CustomContextMenu);
    treeView->setUniformRowHeights(true);
    treeView->header()->setStretchLastSection(true);
    treeView->header()->setDefaultAlignment(Qt::AlignCenter);

    // 设置单位列的委托
    treeView->setItemDelegateForColumn(2, m_unitDelegate); // 第3列（索引2）是单位列

    return treeView;
}

QStandardItemModel* FcMonitorWidget::createDataModel(const QString& schemeName)
{
    QStandardItemModel* model = new QStandardItemModel(this);

    // 设置表头
    QStringList headers;
    //headers << "名称" << "值" << "状态" << "更新时间";
    model->setHorizontalHeaderLabels(QStringList() << tr("名称") << tr("信号值") << tr("单位") << tr("信号类型")
                                                   << tr("起始字") << tr("起始位") << tr("状态") << tr("长度")
                                                   << tr("原始值") << tr("最大值") << tr("最小值"));

    return model;
}

void FcMonitorWidget::buildTreeModel(QStandardItemModel* model, const FcSchemeData& scheme)
{
    model->clear();

    // 清空TreeView项索引
    FcSchemeData* mutableScheme = m_dataManager->getScheme(m_currentSchemeName);
    if (mutableScheme) {
        mutableScheme->clearSignalTreeItemIndex();
    }

    // 重新设置表头
    model->setHorizontalHeaderLabels(QStringList() << tr("名称") << tr("信号值") << tr("单位") << tr("信号类型")
                                                   << tr("起始字") << tr("起始位") << tr("状态") << tr("长度")
                                                   << tr("原始值") << tr("最大值") << tr("最小值"));


    // 添加每个功能单元消息作为第一层级
    for (const auto& unitMessage : scheme.unitMessages) {
        addUnitMessageToModel(model, unitMessage);
    }
}

void FcMonitorWidget::addUnitMessageToModel(QStandardItemModel* model, const FcUnitMessageData& unitMessage)
{
    // 创建功能单元消息节点（第一层级）
    QStandardItem* messageItem = new QStandardItem(unitMessage.messageName);
    QStandardItem* valueItem = new QStandardItem("");
    QStandardItem* unitItem = new QStandardItem("");
    QStandardItem* typeItem = new QStandardItem("");
    QStandardItem* startWordItem = new QStandardItem("");
    QStandardItem* startBitItem = new QStandardItem("");
    QStandardItem* statusItem = new QStandardItem("");  //状态列
    QStandardItem* lengthItem = new QStandardItem(""); // 长度信息
    QStandardItem* rawValueItem = new QStandardItem(""); // 原始值，可能需要额外处理
    QStandardItem* maxValueItem = new QStandardItem("");
    QStandardItem* minValueItem = new QStandardItem("");


    // 设置消息节点的用户数据
    QString msgId = QString::number(unitMessage.sourceFuncId) + "_" + QString::number(unitMessage.pubSubTopicId);
    messageItem->setData(msgId, Qt::UserRole);

    // 添加到模型
    model->appendRow(QList<QStandardItem*>() << messageItem << valueItem << unitItem
                     << typeItem << startWordItem << startBitItem << statusItem << lengthItem
                     << rawValueItem << maxValueItem << minValueItem );

    // 将信号项添加到TreeView索引中
    FcSchemeData* scheme = m_dataManager->getScheme(m_currentSchemeName);
    if (scheme) {
        scheme->addSignalTreeItem(unitMessage.sourceFuncId, unitMessage.pubSubTopicId, "0", messageItem);
        qDebug() << "Added signal to TreeView index:" ;
    }

    if (!unitMessage.topicData.topicName.isEmpty()) {
        addHeadToModel(messageItem, unitMessage.topicData, unitMessage.messageId,
                       unitMessage.sourceFuncId, unitMessage.pubSubTopicId);
    }

    // 添加主题作为第二层级
    if (!unitMessage.topicData.topicName.isEmpty()) {
        addTopicToModel(messageItem, unitMessage.topicData, unitMessage.messageId,
                       unitMessage.sourceFuncId, unitMessage.pubSubTopicId);
    }

}

void FcMonitorWidget::addHeadToModel(QStandardItem* parentItem, const FcTopicData& topic, const QString& messageId,
                                      int sourceFuncId, int pubSubTopicId)
{
    if (!parentItem) {
        return;
    }
    
    // 创建帧头数据节点
    QStandardItem* headItem = new QStandardItem("帧头数据");
    QStandardItem* headValueItem = new QStandardItem("");
    QStandardItem* headUnitItem = new QStandardItem("");
    QStandardItem* headTypeItem = new QStandardItem("");
    QStandardItem* headStartWordItem = new QStandardItem("");
    QStandardItem* headStartBitItem = new QStandardItem("");
    QStandardItem* headStatusItem = new QStandardItem("");
    QStandardItem* headLengthItem = new QStandardItem("");
    QStandardItem* headRawValueItem = new QStandardItem("");
    QStandardItem* headMaxValueItem = new QStandardItem("");
    QStandardItem* headMinValueItem = new QStandardItem("");
    
    // 添加到父节点
    parentItem->appendRow(QList<QStandardItem*>() << headItem << headValueItem << headUnitItem
                         << headTypeItem << headStartWordItem << headStartBitItem << headStatusItem
                         << headLengthItem << headRawValueItem << headMaxValueItem << headMinValueItem);
    
    // 帧头字段定义
    struct FrameHeaderField {
        QString name;
        QString key;
    };
    
    QList<FrameHeaderField> headerFields = {
        {"TYPE", "TYPE"},
        {"S_ID", "SID"},
        {"D_ID", "DID"},
        {"asmMsgId", "asmMsgId"},
        {"uiSource", "AOXEuiSource"},
        {"uiDest", "AOXEuiDest"},
        {"uiMsgSpec", "AOXEuiMsgSpec"},
        {"uiTopic", "AOXEuiTopicId"},
        {"uiTimeTag", "AOXEuiTimeTag"},
        {"uiQos", "AOXEuiQos"},
        {"uiLength", "AOXEuiLength"},
        {"uiBitMap[0]", "AOXEuiBitMap[0]"},
        {"uiBitMap[1]", "AOXEuiBitMap[1]"},
        {"uiBitMap[2]", "AOXEuiBitMap[2]"},
        {"uiBitMap[3]", "AOXEuiBitMap[3]"}
    };
    
    // 获取方案对象用于添加到TreeView索引
    FcSchemeData* scheme = m_dataManager->getScheme(m_currentSchemeName);
    
    // 为每个帧头字段创建节点
    for (const auto& field : headerFields) {
        QStandardItem* fieldItem = new QStandardItem(field.name);
        QStandardItem* fieldValueItem = new QStandardItem("");
        QStandardItem* fieldUnitItem = new QStandardItem("");
        QStandardItem* fieldTypeItem = new QStandardItem("");
        QStandardItem* fieldStartWordItem = new QStandardItem("");
        QStandardItem* fieldStartBitItem = new QStandardItem("");
        QStandardItem* fieldStatusItem = new QStandardItem("");
        QStandardItem* fieldLengthItem = new QStandardItem("");
        QStandardItem* fieldRawValueItem = new QStandardItem("");
        QStandardItem* fieldMaxValueItem = new QStandardItem("");
        QStandardItem* fieldMinValueItem = new QStandardItem("");
        
        // 设置字段的用户数据
        QString fieldKey = QString::number(sourceFuncId) + "_" + QString::number(pubSubTopicId) + "_" + field.key;
        fieldItem->setData(fieldKey, Qt::UserRole);
        
        // 设置字段样式
        fieldItem->setForeground(QBrush(QColor(128, 128, 128))); // 灰色表示帧头字段
        
        // 添加字段到帧头节点
        headItem->appendRow(QList<QStandardItem*>() << fieldItem << fieldValueItem << fieldUnitItem
                           << fieldTypeItem << fieldStartWordItem << fieldStartBitItem << fieldStatusItem
                           << fieldLengthItem << fieldRawValueItem << fieldMaxValueItem << fieldMinValueItem);
        
        // 将字段项添加到TreeView索引中
        if (scheme) {
            scheme->addSignalTreeItem(sourceFuncId, pubSubTopicId, field.key, fieldItem);
        }
    }
}

void FcMonitorWidget::addTopicToModel(QStandardItem* parentItem, const FcTopicData& topic, const QString& messageId,
                                      int sourceFuncId, int pubSubTopicId)
{
    // 创建主题节点（第二层级 - topic标签的shortName属性）
    QString displayName = topic.topicIdentifier;
    if (!topic.topicName.isEmpty()) {
        displayName += QString(" (%1)").arg(topic.topicName);
    }

    QStandardItem* topicItem = new QStandardItem(displayName);
    QStandardItem* valueItem = new QStandardItem("");
    QStandardItem* unitItem = new QStandardItem("");
    QStandardItem* typeItem = new QStandardItem("");
    QStandardItem* startWordItem = new QStandardItem("");
    QStandardItem* startBitItem = new QStandardItem("");
    QStandardItem* statusItem = new QStandardItem("");  //状态列
    QStandardItem* lengthItem = new QStandardItem(""); // 长度信息
    QStandardItem* rawValueItem = new QStandardItem(""); // 原始值，可能需要额外处理
    QStandardItem* maxValueItem = new QStandardItem("");
    QStandardItem* minValueItem = new QStandardItem("");

    // 设置主题节点的用户数据
    QString topicKey = QString("%1_%2").arg(messageId, topic.topicIdentifier);
    topicItem->setData(topicKey, Qt::UserRole);

    // 添加到父节点
    parentItem->appendRow(QList<QStandardItem*>() << topicItem << valueItem << unitItem
                     << typeItem << startWordItem << startBitItem << statusItem << lengthItem
                     << rawValueItem << maxValueItem << minValueItem );


    // 添加信号作为第三层级（Signal标签，处理name中的/层级关系）
    for (const auto& signal : topic.signalList) {
        addSignalToModel(topicItem, signal, topicKey, sourceFuncId, pubSubTopicId);
    }
}

void FcMonitorWidget::addSignalToModel(QStandardItem* parentItem, const FcSignalData& signal, const QString& topicId,
                                       int sourceFuncId, int pubSubTopicId)
{
    // 只显示isActive为true的信号
    if (!signal.isActive) {
        return;
    }

    // 处理Signal中name含有/的层级关系（同SchemeManagement中Signal标签处理方式）
    QStringList pathParts = signal.signalName.split('/', QString::SkipEmptyParts);

    if (pathParts.isEmpty()) {
        // 如果没有路径分隔符，直接添加信号
        // 按表头顺序创建所有列：名称、信号值、单位、信号类型、起始字、起始位、状态、长度、原始值、最大值、最小值
        QStandardItem* nameItem = new QStandardItem(signal.signalName);
        QStandardItem* valueItem = new QStandardItem(signal.currentValue);
        // 只有最子级信号（没有嵌套信号的信号）才使用UnitStandardItem
        UnitStandardItem* unitItem = new UnitStandardItem(signal.unitCode);
        QStandardItem* typeItem = new QStandardItem(signal.signalType);
        QStandardItem* startWordItem = new QStandardItem(signal.startWord);
        QStandardItem* startBitItem = new QStandardItem(signal.startBit);
        QStandardItem* statusItem = new QStandardItem("");  //状态列
        QStandardItem* lengthItem = new QStandardItem(QString::number(signal.signalSize)); // 长度信息
        QStandardItem* rawValueItem = new QStandardItem(""); // 原始值，可能需要额外处理
        QStandardItem* maxValueItem = new QStandardItem(QString::number(signal.maxValue));
        QStandardItem* minValueItem = new QStandardItem(QString::number(signal.minValue));

        //todo begin
        //处理单位item - 只有最子级信号才支持单位切换
        if (signal.nestedSignals.isEmpty()) {
            //获取单位名称
            QString strUnitNameShow = UnitModel::Instance()->GetNameFromID(signal.unitCode);
            //如果有单位
            if(signal.unitCode != "0" && signal.unitCode != "")
            {
                //获取单位列表
                QStringList UnitNameList = UnitModel::Instance()->GetUnitNameListByUnitID(signal.unitCode);
                //单位列表为空
                if (UnitNameList.size() == 0)
                {
                    //unitItem不变，显示原始单位代码
                    unitItem->setText(signal.unitCode);
                }
                //如果只有一个单位
                else if(UnitNameList.size() == 1)
                {
                    unitItem->setText(strUnitNameShow);
                }
                else
                {
                    //unitItem设置为支持单位切换
                    unitItem->setIsHaveCombox(true);
                    unitItem->setUnitList(UnitNameList);
                    unitItem->setDefaultUnitID(signal.unitCode.toUInt());
                    unitItem->setDefaultUnitName(strUnitNameShow);
                    unitItem->setCurrentUnitID(signal.unitCode.toUInt()); // 初始为默认单位
                    unitItem->setCurrentUnitName(strUnitNameShow); // 初始为默认单位名
                    //默认显示为默认单位
                    unitItem->setText(strUnitNameShow);
                }
            }
            else
            {
                // 没有单位或单位为0，显示空
                unitItem->setText("");
            }
        }
        else
        {
            // 有嵌套信号的信号不支持单位切换，直接显示单位代码
            unitItem->setText(signal.unitCode);
        }

        // 设置信号节点的用户数据
        QString signalKey = QString("%1_%2_%3").arg(QString::number(sourceFuncId),QString::number(pubSubTopicId),signal.signalId);
        nameItem->setData(signalKey, Qt::UserRole);
        valueItem->setData(signalKey,Qt::UserRole);

        // 设置样式 - 活跃信号用绿色和蓝色
        statusItem->setForeground(QBrush(QColor(0, 128, 0))); // 绿色
        valueItem->setForeground(QBrush(QColor(0, 0, 255))); // 蓝色

        // 按表头顺序添加所有列
        parentItem->appendRow(QList<QStandardItem*>() << nameItem << valueItem << unitItem << typeItem
                             << startWordItem << startBitItem << statusItem << lengthItem
                             << rawValueItem << maxValueItem << minValueItem);

        // 将信号项添加到TreeView索引中
        FcSchemeData* scheme = m_dataManager->getScheme(m_currentSchemeName);
        if (scheme) {
            scheme->addSignalTreeItem(sourceFuncId, pubSubTopicId, signal.signalId, nameItem);
            qDebug() << "Added signal to TreeView index:" << sourceFuncId << pubSubTopicId << signal.signalId;
        }
    } else {
        // 递归创建层级结构
        addSignalRecursive(parentItem, pathParts, signal, sourceFuncId, pubSubTopicId);
    }
}

QStandardItem* FcMonitorWidget::addSignalRecursive(QStandardItem* parentItem, const QStringList& pathParts, const FcSignalData& signal,
                                                   int sourceFuncId, int pubSubTopicId)
{
    if (pathParts.isEmpty()) {
        return nullptr;
    }

    // 只处理isActive为true的信号
    if (!signal.isActive) {
        return nullptr;
    }

    QString currentPart = pathParts.first();
    QStringList remainingParts = pathParts.mid(1);

    // 查找是否已经存在同名的子节点
    QStandardItem* existingItem = nullptr;
    for (int i = 0; i < parentItem->rowCount(); ++i) {
        QStandardItem* child = parentItem->child(i, 0);
        if (child && child->text() == currentPart) {
            existingItem = child;
            break;
        }
    }

    QStandardItem* currentItem = existingItem;

    if (!currentItem) {
        // 创建新的节点 - 按表头顺序创建所有列
        currentItem = new QStandardItem(currentPart);
        QStandardItem* valueItem = new QStandardItem("");
        UnitStandardItem* unitItem = new UnitStandardItem(signal.unitCode);
        QStandardItem* typeItem = new QStandardItem("");
        QStandardItem* startWordItem = new QStandardItem("");
        QStandardItem* startBitItem = new QStandardItem("");
        QStandardItem* statusItem = new QStandardItem("");
        QStandardItem* lengthItem = new QStandardItem("");
        QStandardItem* rawValueItem = new QStandardItem("");
        QStandardItem* maxValueItem = new QStandardItem("");
        QStandardItem* minValueItem = new QStandardItem("");

        if (remainingParts.isEmpty()) {
            // 这是最后一级，设置信号数据
            valueItem->setText(signal.currentValue);
            // 层级信号不支持单位切换，直接显示单位代码或名称
            QString strUnitNameShow = UnitModel::Instance()->GetNameFromID(signal.unitCode);
            if (!strUnitNameShow.isEmpty() && signal.unitCode != "0") {
                //获取单位列表
                QStringList UnitNameList = UnitModel::Instance()->GetUnitNameListByUnitID(signal.unitCode);
                //单位列表为空
                if (UnitNameList.size() == 0)
                {
                    //unitItem不变，显示原始单位代码
                    unitItem->setText(signal.unitCode);
                }
                //如果只有一个单位
                else if(UnitNameList.size() == 1)
                {
                    unitItem->setText(strUnitNameShow);
                }
                else
                {
                    //unitItem设置为支持单位切换
                    unitItem->setIsHaveCombox(true);
                    unitItem->setUnitList(UnitNameList);
                    unitItem->setDefaultUnitID(signal.unitCode.toUInt());
                    unitItem->setDefaultUnitName(strUnitNameShow);
                    unitItem->setCurrentUnitID(signal.unitCode.toUInt()); // 初始为默认单位
                    unitItem->setCurrentUnitName(strUnitNameShow); // 初始为默认单位名
                    //默认显示为默认单位
                    unitItem->setText(strUnitNameShow);
                }
            } else {
                unitItem->setText(signal.unitCode);
            }
            typeItem->setText(signal.signalType);
            startWordItem->setText(signal.startWord);
            startBitItem->setText(signal.startBit);
            statusItem->setText("活跃");
            lengthItem->setText(QString::number(signal.signalSize)); // 长度信息可能需要从其他字段获取
            rawValueItem->setText(""); // 原始值，可能需要额外处理
            maxValueItem->setText(QString::number(signal.maxValue));
            minValueItem->setText(QString::number(signal.minValue));

            // 设置用户数据
            // 设置信号节点的用户数据
            QString signalKey = QString("%1_%2_%3").arg(QString::number(sourceFuncId),QString::number(pubSubTopicId),signal.signalId);
            valueItem->setData(signalKey,Qt::UserRole);
            currentItem->setData(signalKey, Qt::UserRole + 1);

            // 设置样式 - 活跃信号用绿色和蓝色
            statusItem->setForeground(QBrush(QColor(0, 128, 0))); // 绿色
            valueItem->setForeground(QBrush(QColor(0, 0, 255))); // 蓝色

            // 将信号项添加到TreeView索引中
            FcSchemeData* scheme = m_dataManager->getScheme(m_currentSchemeName);
            if (scheme) {
                scheme->addSignalTreeItem(sourceFuncId, pubSubTopicId, signal.signalId, currentItem);
                qDebug() << "Added recursive signal to TreeView index:" << sourceFuncId << pubSubTopicId << signal.signalId;
            }
        } else {
            // 这是中间层级，设置为分组节点
            valueItem->setText("信号组");
            currentItem->setData("group", Qt::UserRole + 1);
        }

        // 按表头顺序添加所有列
        parentItem->appendRow(QList<QStandardItem*>() << currentItem << valueItem << unitItem << typeItem
                             << startWordItem << startBitItem << statusItem << lengthItem
                             << rawValueItem << maxValueItem << minValueItem);
    }

    if (!remainingParts.isEmpty()) {
        // 递归处理剩余路径
        return addSignalRecursive(currentItem, remainingParts, signal, sourceFuncId, pubSubTopicId);
    }

    return currentItem;
}

void FcMonitorWidget::updateTreeViewData()
{
    if (m_currentSchemeName.isEmpty() || !m_schemeModels.contains(m_currentSchemeName)) {
        return;
    }

    FcSchemeData* scheme = m_dataManager->getScheme(m_currentSchemeName);
    if (!scheme) {
         return;
    }

    QStandardItemModel* model = m_schemeModels[m_currentSchemeName];

    // 更新模型数据而不重建整个树
    for (int i = 0; i < model->rowCount(); ++i) {
        QStandardItem* messageItem = model->item(i, 0);
        if (!messageItem) continue;

        QString messageId = messageItem->data(Qt::UserRole).toString();
        FcUnitMessageData* unitMessage = scheme->findUnitMessage(messageId);

        if (unitMessage) {
            // 更新消息状态
            model->item(i, 1)->setText(QString("源功能ID: %1").arg(unitMessage->sourceFuncId));
            model->item(i, 2)->setText(unitMessage->isMonitoring ? "监控中" : "未监控");
            model->item(i, 3)->setText(formatTimestamp(unitMessage->lastReceiveTime));

            // 更新主题数据
            updateTopicItems(messageItem, unitMessage);
        }
    }
}

void FcMonitorWidget::updateSignalValueFromTcp(int sourceFuncId, int pubSubTopicId, const QString& signalId, const QString& signalValue)
{
    FcSchemeData* scheme = m_dataManager->getScheme(m_currentSchemeName);
    if (!scheme) {
        return;
    }

    auto signalVueList = signalValue.split("_");
    QString signalValue1 = signalVueList.first();
    QString signalValueHex = signalVueList.last();
    // 根据索引查找对应的TreeView项
    QVector<QStandardItem*> signalItems = scheme->findSignalTreeItems(sourceFuncId, pubSubTopicId, signalId);

    if (signalItems.isEmpty()) {
        qDebug() << "TreeView item not found:" << sourceFuncId << pubSubTopicId << signalId;
        return;
    }

    //上一次的值
    QString lastValue;
    // 更新所有匹配的TreeView项
    for (QStandardItem* signalItem : signalItems) {
        if (signalItem && signalItem->parent()) {
            // 更新信号值（第二列）
            QStandardItem* valueItem = signalItem->parent()->child(signalItem->row(), 1);
            QStandardItem* valueRawItem = signalItem->parent()->child(signalItem->row(), 8);

            //获取单位item (第3列，索引为2)
            QStandardItem* standardUnitItem = signalItem->parent()->child(signalItem->row(), 2);
            UnitStandardItem* unitItem = dynamic_cast<UnitStandardItem*>(standardUnitItem);

            //判断该信号是否有其它单位可供切换
            if(unitItem && unitItem->isHaveCombox())
            {
                //获取单位ID
                unsigned int currentUnitID = unitItem->currentUnitID();
                unsigned int defaultUnitID = unitItem->defaultUnitID();
                QString currentUnitName = unitItem->currentUnitName();

                //将信号值转换为double进行单位转换
                bool ok;
                double dbSignalValue = signalValue1.toDouble(&ok);
                if(!ok) {
                    // 如果转换失败，使用原始值
                    dbSignalValue = 0.0;
                }

                //进行单位转换
                double convertedValue = UnitModel::unit_change(defaultUnitID, currentUnitID, dbSignalValue);

                //针对时分秒单位进行特殊处理
                if (currentUnitName == "时分秒")
                {
                    unsigned int unintSigVal = static_cast<unsigned int>(convertedValue);
                    unsigned int unintHour = unintSigVal / 3600;
                    unsigned int unintMin = (unintSigVal % 3600) / 60;
                    unsigned int unintSec = unintSigVal % 60;
                    QString strSigval = QString("%1:%2:%3")
                                       .arg(unintHour, 2, 10, QChar('0'))
                                       .arg(unintMin, 2, 10, QChar('0'))
                                       .arg(unintSec, 2, 10, QChar('0'));
                    signalValue1 = strSigval;
                }
                else if (currentUnitName == QString::fromLocal8Bit("度分秒"))
                {
                    unsigned int uintDeg = static_cast<unsigned int>(convertedValue);
                    unsigned int uintDegM = static_cast<unsigned int>((convertedValue - uintDeg) * 60);
                    unsigned int uintDegS = static_cast<unsigned int>((convertedValue - uintDeg - static_cast<double>(uintDegM) / 60) * 3600);
                    QString strSigval = QString("%1° %2' %3")
                                       .arg(uintDeg, 2, 10, QChar('0'))
                                       .arg(uintDegM, 2, 10, QChar('0'))
                                       .arg(uintDegS, 2, 10, QChar('0'));
                    signalValue1 = strSigval;
                }
                else if (currentUnitName == QString::fromLocal8Bit("度分"))
                {
                    unsigned int uintDeg = static_cast<unsigned int>(convertedValue);
                    unsigned int uintDegM = static_cast<unsigned int>((convertedValue - uintDeg) * 60);
                    QString strSigval = QString("%1' %2")
                                       .arg(uintDeg, 2, 10, QChar('0'))
                                       .arg(uintDegM, 2, 10, QChar('0'));
                    signalValue1 = strSigval;
                }
                else
                {
                    // 普通数值单位，直接显示转换后的值
                    signalValue1 = QString::number(convertedValue, 'f', 6); // 保留6位小数
                }
            }

            if (valueItem) {
                lastValue = valueRawItem->text();
                valueItem->setText(signalValue1);
                valueRawItem->setText(signalValueHex);
                // 设置样式表示数据已更新
                valueItem->setForeground(QBrush(Qt::black)); // 黑色
                valueRawItem->setForeground(QBrush(Qt::black)); // 黑色
                if(lastValue != signalValueHex)
                {
                     valueItem->setForeground(QBrush(Qt::red)); // 黑色
                     valueRawItem->setForeground(QBrush(Qt::red)); // 黑色
                }
            }
        }
        //更新时间和类型 没有父级
        else {
            if(signalItem)
            {
                // 更新信号值（第二列）
                // 1. 获取模型和位置
                QStandardItemModel* model = qobject_cast<QStandardItemModel*>(signalItem->model());
                int row = signalItem->row();

                QStandardItem* valueItem =  model->item(row, 1);
                QStandardItem* valueRawItem = model->item(row, 3);
                    lastValue = valueRawItem->text();
                    signalValue1 = 1? "来源:FC" :"来源:1394";
                    valueItem->setText(signalValue1);
                    valueRawItem->setText(signalValueHex);
                    // 更新其他列:时间戳
                    // 设置样式表示数据已更新
                    valueItem->setForeground(QBrush(Qt::black)); // 黑色
                    valueRawItem->setForeground(QBrush(Qt::black)); // 黑色
                    //数据值和上一帧不一样设置为红色
                    if(lastValue != signalValueHex)
                    {
                        valueItem->setForeground(QBrush(Qt::red)); // 红色
                        valueRawItem->setForeground(QBrush(Qt::red)); // 黑色
                }
            }
        }
    }
}

void FcMonitorWidget::updateTopicItems(QStandardItem* messageItem, FcUnitMessageData* unitMessage)
{
    for (int j = 0; j < messageItem->rowCount(); ++j) {
        QStandardItem* topicItem = messageItem->child(j, 0);
        if (!topicItem) continue;

        QString topicKey = topicItem->data(Qt::UserRole).toString();
        QString topicIdentifier = topicItem->data(Qt::UserRole + 2).toString();

        // 使用unitMessage的topicData
        FcTopicData* topic = &unitMessage->topicData;
        if (topic && (topic->topicIdentifier == topicIdentifier || topicKey.contains(topicIdentifier))) {
            messageItem->child(j, 1)->setText(QString("信号数: %1").arg(topic->signalList.size()));
            messageItem->child(j, 2)->setText(topic->isActive ? "活跃" : "非活跃");
            messageItem->child(j, 3)->setText(formatTimestamp(topic->lastUpdateTime));

            // 更新信号数据
            updateSignalItems(topicItem, topic);
        }
    }
}

void FcMonitorWidget::updateSignalItems(QStandardItem* topicItem, FcTopicData* topic)
{
    // 递归更新信号项目
    updateSignalItemsRecursive(topicItem, topic);
}

void FcMonitorWidget::updateSignalItemsRecursive(QStandardItem* parentItem, FcTopicData* topic)
{
    for (int k = 0; k < parentItem->rowCount(); ++k) {
        QStandardItem* signalItem = parentItem->child(k, 0);
        if (!signalItem) continue;

        QString itemType = signalItem->data(Qt::UserRole + 1).toString();

        if (itemType == "signal") {
            // 这是信号节点，更新数据
            QString signalIdentifier = signalItem->data(Qt::UserRole + 2).toString();
            FcSignalData* signal = topic->signalIndex.value(signalIdentifier, nullptr);

            if (signal && signal->isActive) {
                // 按表头顺序更新所有列：名称、信号值、单位、信号类型、起始字、起始位、状态、长度、原始值、最大值、最小值
                parentItem->child(k, 1)->setText(signal->currentValue);           // 信号值
                parentItem->child(k, 2)->setText(signal->unitCode);               // 单位
                parentItem->child(k, 3)->setText(signal->signalType);             // 信号类型
                parentItem->child(k, 4)->setText(signal->startWord);              // 起始字
                parentItem->child(k, 5)->setText(signal->startBit);               // 起始位
                parentItem->child(k, 6)->setText("活跃");                          // 状态
                parentItem->child(k, 7)->setText("");                             // 长度
                parentItem->child(k, 8)->setText("");                             // 原始值
                parentItem->child(k, 9)->setText(QString::number(signal->maxValue)); // 最大值
                parentItem->child(k, 10)->setText(QString::number(signal->minValue)); // 最小值
            } else if (signal && !signal->isActive) {
                // 如果信号变为非活跃状态，应该从树中移除该节点
                // 但这里我们只是不更新它，因为在addSignalToModel中已经过滤了非活跃信号
            }
        } else if (itemType == "group") {
            // 这是分组节点，递归更新子节点
            updateSignalItemsRecursive(signalItem, topic);
        }
    }
}

void FcMonitorWidget::updateSchemeList()
{
    ui->schemeListWidget->clear();

    QStringList schemeNames = m_dataManager->getSchemeNames();
    for (const QString& schemeName : schemeNames) {
        QListWidgetItem* item = createSchemeListItem(schemeName,m_dataManager->getScheme(schemeName)->isShared);
        ui->schemeListWidget->addItem(item);
    }
}

QListWidgetItem* FcMonitorWidget::createSchemeListItem(const QString& schemeName, bool isShared)
{
    QListWidgetItem* item = new QListWidgetItem();

    SchemeListItem *schemeItem = new SchemeListItem(schemeName,ui->schemeListWidget);
    //获取该方案的分享状态
    schemeItem->setShareStatus(isShared);

    item->setData(Qt::UserRole, schemeName);
    item->setSizeHint(schemeItem->sizeHint());
    ui->schemeListWidget->addItem(item);
    ui->schemeListWidget->setItemWidget(item,schemeItem);

    // 连接删除点击信号
    connect(schemeItem, &SchemeListItem::deleteFinished, this, [=]() {
        // 从列表中删除该项
        int row = ui->schemeListWidget->row(item);
        if (row >= 0) {
            ui->schemeListWidget->takeItem(row);
            delete item;
        }
    });
    connect(schemeItem->m_schemeEditDialog,&SchemeEditDialog::editSchemefinish,this,&FcMonitorWidget::addSchemefinished);
    return item;
}

void FcMonitorWidget::optimizeTreeViewPerformance(QTreeView* treeView)
{
    if (!treeView)
        return;

    // 启用延迟加载，提高大型树的性能
    treeView->setUniformRowHeights(true);

    // 减少重绘操作
    treeView->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);
    treeView->setHorizontalScrollMode(QAbstractItemView::ScrollPerPixel);

    // 优化滚动条性能
    treeView->verticalScrollBar()->setSingleStep(20);

    // 启用缓存背景，减少重绘 - 兼容 Qt 5.15.2
    treeView->setAttribute(Qt::WA_StaticContents, true);

    // 禁用动画效果
    treeView->setAnimated(false);

    // 优化选择行为
    treeView->setSelectionBehavior(QAbstractItemView::SelectRows);

    // 优化选择模式 - 禁用多选可以提高性能
    treeView->setSelectionMode(QAbstractItemView::SingleSelection);

    // 设置拖放模式为空，以提高性能
    treeView->setDragDropMode(QAbstractItemView::NoDragDrop);

    // 禁用自动展开，让用户控制展开/折叠
    treeView->setAutoExpandDelay(-1);

    // 禁用排序，避免不必要的排序计算开销
    treeView->setSortingEnabled(false);

    // 设置表头可手动调整大小 - 使用Interactive模式
    treeView->header()->setSectionResizeMode(QHeaderView::Interactive);

    // 确保最后一列拉伸填充剩余空间
    treeView->header()->setStretchLastSection(true);

    // 禁用工具提示，可能会降低性能
    treeView->setToolTip("");
    treeView->setAttribute(Qt::WA_AlwaysShowToolTips, false);

    // 设置样式
    treeView->header()->setDefaultSectionSize(150);
    treeView->setAlternatingRowColors(true);
    treeView->setStyleSheet(
        "QTreeView::item:hover { background: none; }"
        "QTreeView::branch { image: none; }"
        "QTreeView::branch:has-siblings:!adjoins-item {border-image: url(:/images/_vline__.png) 0;}"
        "QTreeView::branch:has-siblings:adjoins-item {border-image: url(:/images/_more__.png) 0;}"
        "QTreeView::branch:!has-children:!has-siblings:adjoins-item {border-image: url(:/images/_end__.png) 0;}"
        "QTreeView::branch:has-children:!has-siblings:closed,"
        "QTreeView::branch:closed:has-children:has-siblings {border-image: none;image: url(:/images/_close__.png);}"
        "QTreeView::branch:open:has-children:!has-siblings,"
        "QTreeView::branch:open:has-children:has-siblings  {"
        "border-image: none;"
        "image: url(:/images/_open__.png);}"
        "QTreeView::item{min-height: 8px;border:1px solid rgb(154,182,220);border-left-color:transparent;border-top-color:transparent;}"
        "QTreeView::branch{border-bottom:1px solid rgb(154,182,220);background:palette(base);}"
        );
    treeView->horizontalScrollBar()->setStyleSheet(" QScrollBar:horizontal{background: white;}QScrollBar::handle:horizontal{background: none; border: 2px solid rgb(191, 219, 255); image: url(:/images/hander.png);margin: 0px 16px 0 16px;min-width: 30px;}QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal{background: none;}");
    treeView->verticalScrollBar()->setStyleSheet("QScrollBar:vertical{background: white;}QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical{background: none;}QScrollBar::handle:vertical{background: none; border: 2px solid rgb(191, 219, 255); image: url(:/images/hander.png);margin: 16px 0px 16 0px;min-height: 30px;}");
    treeView->setSelectionMode(QAbstractItemView::NoSelection);

    treeView->setColumnWidth(0,200);
    treeView->setColumnWidth(1,300);
    treeView->setColumnWidth(2,100);
    treeView->setColumnWidth(3,100);
    treeView->setColumnWidth(4,60);
    treeView->setColumnWidth(5,60);
    treeView->setColumnWidth(6,60);
    treeView->setColumnWidth(7,60);
    treeView->setColumnWidth(8,200);

}

void FcMonitorWidget::updateMonitoringState()
{
    // 更新按钮状态

    ui->pauseButton->setText(m_isPaused ? "继续" : "暂停");
}

//根据ResponseData更新TreeView
void FcMonitorWidget::updateTreeViewFromResponseData(const QMap<QString, QVector<QString>>& dataMap,
                                                     const QSet<QString>& updatedKeys)
{

    FcSchemeData* scheme = m_dataManager->getScheme(m_currentSchemeName);
    if (!scheme) {
        return;
    }

    //这里等于遍历updatedKeys的话，只会更新变化的
    //这样不能处理有变化为红色，没变化为黑色的逻辑，所以所有数据都处理一遍
    for (const QString& treeViewKey : dataMap.keys()) {
        QStringList keyParts = treeViewKey.split('_');
        if (keyParts.size() != 3) {
            continue;
        }

        bool ok1, ok2;
        int sourceFuncId = keyParts[0].toInt(&ok1);
        int pubSubTopicId = keyParts[1].toInt(&ok2);
        QString signalId = keyParts[2];

        if (!ok1 || !ok2) {
            continue;
        }

        QVector<QString> signalData = dataMap.value(treeViewKey);
        if (signalData.isEmpty()) {
            continue;
        }

        QString signalValue = signalData[0] + "_" + signalData[1];

        // 使用现有的TreeView索引快速更新
        updateSignalValueFromTcp(sourceFuncId, pubSubTopicId, signalId, signalValue);
    }
}

QString FcMonitorWidget::formatTimestamp(const QDateTime& timestamp)
{
    if (!timestamp.isValid()) {
        return "未更新";
    }
    return timestamp.toString("hh:mm:ss.zzz");
}

QString FcMonitorWidget::formatDataValue(const QString& value, const QString& dataType)
{
    if (value.isEmpty()) {
        return "无数据";
    }

    if (dataType == "DATA_BLOCK") {
        // 限制显示长度，避免界面过于拥挤
        if (value.length() > 20) {
            return value.left(17) + "...";
        }
    }

    return value;
}

// 槽函数实现
void FcMonitorWidget::onSchemeItemClicked(QListWidgetItem* item)
{
    if (!item) return;

    QString schemeName = item->data(Qt::UserRole).toString();
    if (schemeName != m_currentSchemeName) {
        QString oldSchemeName = m_currentSchemeName;
        m_currentSchemeName = schemeName;

        // 更新订阅规则
        updateSubscriptionRules(schemeName);

        loadSchemeDataView(schemeName);
        //updateSchemeList(); // 更新选中状态
        //emit schemeSelected(schemeName);
    }
}

void FcMonitorWidget::onTreeItemClicked(const QModelIndex& index)
{
    //没有暂停，直接返回
    if(!m_isPaused) return;
    if (!index.isValid() || !timeListWidget) return;

    // 检查是否为叶子节点（无子节点）
    if (index.model()->hasChildren(index)) {
        QMessageBox::information(this, tr("提示"), tr("请选择一个信号"));
        return;
    }

    timeListWidget->show();
    timeListWidget->clear();

    // 获取点击项的父级根节点
    QModelIndex parentIndex = index.parent();
    while (parentIndex.parent().isValid()) {
        parentIndex = parentIndex.parent();
    }


    // 获取根节点的key
    // 这里的key为: provideSrvID_callerSrvID_interfaceID_topicValue_signalID
     //QString signalkey = parentIndex.data(Qt::UserRole).toString();
     QString signalkey = index.data(Qt::UserRole).toString();
     if (signalkey.isEmpty()) {
         return;
     }

     QStringList queryKey = signalkey.split("_");
     queryKey.removeLast();
     QString key = queryKey.join("_");

     // 从UnpackDataStore获取数据
     HistoryDataStore& store = HistoryDataStore::getInstance();
     // 保存当前数据列表供TimeListWidgetRowChanged使用
     m_currentTimeDataList.clear();
     m_currentTimeDataList = store.getData(key);

     //获取信号的序列化编码
     QString signalCode = signalkey.split("_").last();

     // 遍历unpackDataMap，将每个成员的key添加到timelist，并比较m_signalResult中key为signalID的value与前一条是否不同
     QString previousValue;
     for (const HistoryDataStore::HistoryDataItem& data : m_currentTimeDataList) {
         QDateTime dateTime = data.timestamp;
         QString timeKey = dateTime.toString("yyyy-MM-dd HH:mm:ss.zzz");
         QListWidgetItem* item = new QListWidgetItem(timeKey);
         // 存储UnpackData的索引，用于后续查找
         //item->setData(Qt::UserRole,m_currentTimeDataList.indexOf(data));
         timeListWidget->addItem(item);

         // 查找m_signalResult中key为signalID的value
         QString currentValue = "";
         for (const auto& pair : data.signalValueMap) {
             if (QString::fromStdString(pair.first) == signalCode) {
                 currentValue = QString::fromStdString(pair.second);
                 break;
             }
         }
         // 如果当前value与前一条不同，设置timelist对应项为红色
         if (!previousValue.isEmpty() && currentValue != previousValue) {
             // 设置字体颜色为红色
             item->setForeground(QBrush(QColor(Qt::red)));
         }
         previousValue = currentValue;
     }
}

void FcMonitorWidget::onTabChanged(int index)
{
    if (index < 0 || index > ui->dataTabWidget->count())
    {
        // 没有订阅信息，清除当前订阅规则
        SubRuleManager::getInstance().updateRules(this, m_currentBenchId, m_lastRules, QList<SubRuleManager::SubscribeRule>());
        m_lastRules.clear();
        return;
    }

    QString schemeName = ui->dataTabWidget->tabText(index);
    if (schemeName != m_currentSchemeName) {
        m_currentSchemeName = schemeName;

        // 更新订阅规则
        updateSubscriptionRules(schemeName);
    }
}

void FcMonitorWidget::onSchemeAndTemplateTabChanged(int index)
{
    // 标签页切换事件处理
    if (index == 0) {
        // 切换到"我的方案"标签页
        if(m_isDownload)
        {
            loadScheme();
            m_isDownload = false;
        }
    } else if (index == 1) {
        // 切换到"模板库"标签页
        ui->templateListWgt->refreshTemplateList();
    }

    // 确保标签页保持左对齐
//    centerTabBar();
}

void FcMonitorWidget::onSearchTextChanged(const QString& text)
{
    performSearch(text);
}

void FcMonitorWidget::onAddSchemeClicked()
{
    //第一次点开再初始化，构造函数初始化 icd 类还没初始化完成
    if(addSchemeDialog == nullptr)
    {
        //切换ICD时需要重新初始化
        //只创建一次，避免频繁创建解析icd;
        addSchemeDialog = new SchemeAddDialog("新建方案", this);
        addSchemeDialog->hide();
        //这里返回新增方案的结果并处理
        connect(addSchemeDialog,&SchemeAddDialog::addSchemefinish,this,&FcMonitorWidget::addSchemefinished);
    }

    addSchemeDialog->show();
}

void FcMonitorWidget::addSchemefinished(int ret)
{
    if(addSchemeDialog)
    {
        addSchemeDialog->hide();
    }
    if(ret) {
       //这里需要重新解析xml生成方案
       //解析本地的，不从远端下载
        loadScheme();
    }
}

void FcMonitorWidget::editSchemefinished(int ret)
{
    if(ret) {
       //这里需要重新解析xml生成方案
       //解析本地的，不从远端下载
        loadScheme();
    }
}


void FcMonitorWidget::onStartMonitoringClicked()
{
    if (m_currentSchemeName.isEmpty()) {
        QMessageBox::warning(this, "警告", "请先选择一个监控方案");
        return;
    }

    startMonitoring(m_currentSchemeName);
}

void FcMonitorWidget::onStopMonitoringClicked()
{
    if (m_isMonitoring) {
        stopMonitoring(m_currentSchemeName);
    }
}

void FcMonitorWidget::onPauseClicked()
{
    pauseMonitoring();
}

void FcMonitorWidget::onClearDataClicked()
{
    // 获取当前标签页
    int currentIndex = ui->dataTabWidget->currentIndex();
    if (currentIndex < 0) {
        qDebug() << "没有选中的方案，无法清除数据";
        return;
    }

    // 获取当前标签页的TreeView
    QTreeView *treeView = qobject_cast<QTreeView*>(ui->dataTabWidget->widget(currentIndex));
    if (!treeView) {
        qWarning() << "无法获取树视图";
        return;
    }

    // 获取数据模型
    QStandardItemModel *model = qobject_cast<QStandardItemModel*>(treeView->model());
    if (!model) {
        qWarning() << "无法获取数据模型";
        return;
    }

    // 递归清除所有叶子节点的第1列和第8列数据
    clearLeafNodeData(model->invisibleRootItem());

    qDebug() << "已清除当前标签页的显示数据";

}
// 辅助函数：递归清除叶子节点的指定列数据
void FcMonitorWidget::clearLeafNodeData(QStandardItem *parentItem)
{
    if (!parentItem) return;

    // 遍历所有子项
    for (int i = 0; i < parentItem->rowCount(); ++i) {
        QStandardItem *childItem = parentItem->child(i, 0); // 获取第0列的子项
        if (!childItem) continue;

        // 检查是否为叶子节点（没有子项）
        if (childItem->rowCount() == 0) {
            // 这是叶子节点，清除第1列和第8列的数据
            QStandardItem *column1Item = parentItem->child(i, 1); // 第1列（信号值）
            QStandardItem *column8Item = parentItem->child(i, 8); // 第8列（状态或其他数据）

            if (column1Item) {
                column1Item->setText(""); // 清空第1列数据
            }
            if (column8Item) {
                column8Item->setText(""); // 清空第8列数据
            }
        } else {
            // 不是叶子节点，递归处理子节点
            clearLeafNodeData(childItem);
        }
    }
}

void FcMonitorWidget::onExportDataClicked()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "导出监控数据", QString("fc_monitor_data_%1.csv")
        .arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        "CSV文件 (*.csv);;所有文件 (*.*)");

    if (!fileName.isEmpty()) {
        // TODO: 实现数据导出功能
        QMessageBox::information(this, "提示", "数据导出功能待实现");
    }
}

void FcMonitorWidget::onUpdateTimer()
{
    if (!m_isMonitoring || m_isPaused) {
        return;
    }

    if(ui->dataTabWidget->count() == 0)
    {
        return;
    }

    // 1. 从ResponseDataStore获取TreeView更新数据
    QMap<QString, QVector<QString>> treeViewDataMap;
    QSet<QString> updatedKeys;
    bool dataChanged = false;

    ResponseDataStore::getInstance().getTreeViewUpdates(treeViewDataMap, updatedKeys, dataChanged);

    if (dataChanged && !updatedKeys.isEmpty()) {
        // 2. 使用TreeView索引快速更新界面
        updateTreeViewFromResponseData(treeViewDataMap, updatedKeys);
        //qDebug() << "Timer updated TreeView, signal count:" << updatedKeys.size();
    }

}

void FcMonitorWidget::onSchemeDataChanged(const QString& schemeName)
{
    updateSchemeList();
    if (schemeName == m_currentSchemeName) {
        updateTreeViewData();
    }
}

void FcMonitorWidget::onSchemeAdded(const QString& schemeName)
{
    qDebug() << "Scheme added to list:" << schemeName;

    // 更新方案列表显示
    updateSchemeList();

    // 自动选择新添加的方案
    for (int i = 0; i < ui->schemeListWidget->count(); ++i) {
        QListWidgetItem* item = ui->schemeListWidget->item(i);
        if (item && item->text() == schemeName) {
            ui->schemeListWidget->setCurrentItem(item);
            loadSchemeDataView(schemeName);
            break;
        }
    }

    qDebug() << "Scheme list updated, current scheme:" << schemeName;
}

void FcMonitorWidget::onMessageDataUpdated(const QString& schemeName, const QString& messageId)
{
    if (schemeName == m_currentSchemeName) {
        // 触发界面更新将在下次定时器触发时进行
        emit dataUpdated(schemeName);
    }
}

void FcMonitorWidget::onTopicDataUpdated(const QString& schemeName, const QString& messageId, const QString& topicId)
{
    if (schemeName == m_currentSchemeName) {
        // 触发界面更新将在下次定时器触发时进行
        emit dataUpdated(schemeName);
    }
}

void FcMonitorWidget::onParseError(const QString& errorMessage)
{
    qWarning() << "FC data parsing error:" << errorMessage;
    // 可以选择显示错误对话框或在状态栏显示错误信息
    m_statusLabel->setText(QString("解析错误: %1").arg(errorMessage));
}

void FcMonitorWidget::performSearch(const QString& searchText)
{
    if (!m_schemeTreeViews.contains(m_currentSchemeName)) {
        return;
    }

    QTreeView* treeView = m_schemeTreeViews[m_currentSchemeName];
    QStandardItemModel* model = m_schemeModels[m_currentSchemeName];

    if (searchText.isEmpty()) {
        // 显示所有项目
        for (int i = 0; i < model->rowCount(); ++i) {
            treeView->setRowHidden(i, QModelIndex(), false);
            QStandardItem* messageItem = model->item(i, 0);
            for (int j = 0; j < messageItem->rowCount(); ++j) {
                treeView->setRowHidden(j, messageItem->index(), false);
            }
        }
    } else {
        // 根据搜索文本过滤
        for (int i = 0; i < model->rowCount(); ++i) {
            QStandardItem* messageItem = model->item(i, 0);
            bool messageMatch = messageItem->text().contains(searchText, Qt::CaseInsensitive);
            bool hasVisibleChild = false;

            // 检查子项目
            for (int j = 0; j < messageItem->rowCount(); ++j) {
                QStandardItem* topicItem = messageItem->child(j, 0);
                bool topicMatch = topicItem->text().contains(searchText, Qt::CaseInsensitive);
                treeView->setRowHidden(j, messageItem->index(), !topicMatch);
                if (topicMatch) {
                    hasVisibleChild = true;
                }
            }

            // 如果消息匹配或有可见的子项目，则显示消息
            treeView->setRowHidden(i, QModelIndex(), !(messageMatch || hasVisibleChild));
        }
    }
}

void FcMonitorWidget::clearOldData()
{
    // 清理所有方案的运行时数据
    for (const QString& schemeName : m_dataManager->getSchemeNames()) {
        FcSchemeData* scheme = m_dataManager->getScheme(schemeName);
        if (scheme) {
            // 清理功能单元消息数据
            for (auto& unitMessage : scheme->unitMessages) {
                unitMessage.messageCount = 0;
                unitMessage.lastReceiveTime = QDateTime();

                // 清理主题数据
                unitMessage.topicData.lastUpdateTime = QDateTime();
                unitMessage.topicData.isActive = false;
                for (auto& signal : unitMessage.topicData.signalList) {
                    signal.currentValue.clear();
                    signal.lastUpdateTime = QDateTime();
                    signal.isActive = false;
                }
            }

            // 清理独立主题数据
            for (auto& topic : scheme->topics) {
                topic.lastUpdateTime = QDateTime();
                topic.isActive = false;
                for (auto& signal : topic.signalList) {
                    signal.currentValue.clear();
                    signal.lastUpdateTime = QDateTime();
                    signal.isActive = false;
                }
            }
        }
    }
}

void FcMonitorWidget::cleanupResources(const QString& schemeName)
{
    // 清理树视图和模型
    if (m_schemeTreeViews.contains(schemeName)) {
        QTreeView* treeView = m_schemeTreeViews[schemeName];

        // 从标签页中移除
        for (int i = 0; i < ui->dataTabWidget->count(); ++i) {
            if (ui->dataTabWidget->widget(i) == treeView) {
                ui->dataTabWidget->removeTab(i);
                break;
            }
        }

        m_schemeTreeViews.remove(schemeName);
    }

    if (m_schemeModels.contains(schemeName)) {
        QStandardItemModel* model = m_schemeModels[schemeName];
        model->deleteLater();
        m_schemeModels.remove(schemeName);
    }
}

void FcMonitorWidget::onDataTabClosed(int index)
{
    QString schemeName = ui->dataTabWidget->tabText(index);

    QWidget* page = ui->dataTabWidget->widget(index);
    // 移除标签页
    ui->dataTabWidget->removeTab(index);
    page->deleteLater();

    m_schemeTreeViews.remove(schemeName);

    if (m_schemeModels.contains(schemeName)) {
        QStandardItemModel* model = m_schemeModels[schemeName];
        model->deleteLater();
        m_schemeModels.remove(schemeName);
    }
}

/**
 * @brief 从服务器下载XML方案文件并保存到本地
 * 这个函数负责从服务器下载XML格式的方案文件，并将其保存到指定的本地路径。
 * 下载完成后，将通过回调函数通知调用者下载结果。
 */
void FcMonitorWidget::downloadAndSaveXmlFile(const QString& userId, const QString& filePath,
                                              std::function<void(bool success)> callback)
{
    qDebug() << "Starting to download XML scheme file:" << userId;

    // 获取服务器上XML文件的URL

    QUrl url = ApiUrlManager::getInstance().getSchemeXmlUrl(userId);
    if (!url.isValid()) {
        // QMessageBox::warning(this, tr("下载失败"),
        //                      tr("方案文件 %1 的URL无效").arg(userId));
        if (callback) {
            callback(false);
        }
        return;
    }

    // 准备网络请求
    QNetworkRequest request(url);
    request.setRawHeader("accept", "*/*");
    // 获取用户认证令牌
    QString token = UserSession::getInstance().getToken();
    if (!token.isEmpty()) {
        request.setRawHeader("Authorization", token.toUtf8());
    }
    // 创建网络管理器
    QNetworkAccessManager* networkManager = new QNetworkAccessManager(this);

    // 发送GET请求并处理响应
    QNetworkReply* reply = networkManager->get(request);

    // 使用Lambda表达式处理响应
    connect(reply, &QNetworkReply::finished, [this, reply, userId, filePath, networkManager, callback]() {

        bool success = false;
        // 请求完成后，处理响应
        if (reply->error() == QNetworkReply::NoError) {
            // 读取响应数据
            QByteArray responseData = reply->readAll();

            // 将响应数据转换为JSON
            QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
            if (jsonDoc.isObject()) {
                QJsonObject jsonObj = jsonDoc.object();

                // 检查是否存在data字段
                if (jsonObj.contains("data")) {
                    // 获取data字段的内容（下载链接）
                    QJsonValue dataValue = jsonObj["data"];

                    // 如果data是字符串（下载链接）
                    if (dataValue.isString()) {
                        m_loadXmlUrl = dataValue.toString();
                        // 返回true表示开始下载
                        success = true;
                    } else {
                        QMessageBox::warning(this, tr("数据格式错误"),
                                             tr("data字段不是有效的下载链接"));
                        if (callback) {
                            callback(false);
                        }
                    }
                } else {
                    QMessageBox::warning(this, tr("数据格式错误"),
                                         tr("响应中未找到data字段"));
                    if (callback) {
                        callback(false);
                    }
                }
            } else {
                QMessageBox::warning(this, tr("数据格式错误"),
                                     tr("响应不是有效的JSON格式"));
                if (callback) {
                    callback(false);
                }
            }
        }
        else {
            QMessageBox::warning(this, tr("下载失败"),
                                 tr("网络请求错误: %1").arg(reply->errorString()));
        }

        // 执行回调函数，返回下载结果
        if (callback) {
            callback(success);
        }

        // 清理资源
        reply->deleteLater();
        networkManager->deleteLater();
    });

    qDebug() << "XML scheme file download request sent:" << userId << "URL:" << url.toString();
}

// 下载并保存文件的函数
void FcMonitorWidget::downloadAndSaveFile(const QString& fileName, const QString& filePath, std::function<void(bool success)> callback)
{
    // 直接使用FileItem中的URL进行下载
    QUrl url(m_loadXmlUrl);
    if (!url.isValid()) {
        qWarning() << "Invalid file URL:" << m_loadXmlUrl;
        // QMessageBox::warning(this, tr("下载失败"),
        //                      tr("文件 %1 的URL无效").arg(fileName));
        if (callback) {
            callback(false);
        }
        return;
    }

    // 准备网络请求
    QNetworkRequest request(url);
    request.setRawHeader("accept", "*/*");

    // 获取用户会话数据
//     QString token = UserSession::getInstance().getToken();
//     if (!token.isEmpty()) {
//         request.setRawHeader("Authorization", token.toUtf8());
//     }

    // 创建网络管理器
    QNetworkAccessManager* networkManager = new QNetworkAccessManager(this);

    // 发送GET请求并处理响应
    QNetworkReply* reply = networkManager->get(request);

    // 使用Lambda表达式处理响应
    connect(reply, &QNetworkReply::finished, [this, reply, fileName, filePath, networkManager, callback]() {
        bool success = false;

        // 请求完成后，处理响应
        if (reply->error() == QNetworkReply::NoError) {
            // 读取响应数据
            QByteArray responseData = reply->readAll();

            // 直接保存响应内容到文件
            QFile file(filePath);
            if (file.open(QIODevice::WriteOnly)) {
                file.write(responseData);
                file.close();

                qDebug() << "File successfully downloaded and saved locally:" << filePath;
//                QMessageBox::information(this, tr("下载成功"),
//                                         tr("文件 %1 已成功下载并保存到本地").arg(fileName));
                success = true;
            }
            else {
                qWarning() << "Unable to save file:" << file.errorString();
                QMessageBox::warning(this, tr("保存失败"),
                                     tr("无法保存文件 %1: %2").arg(fileName).arg(file.errorString()));
            }
        }
        else {
            qWarning() << "Network request error:" << reply->errorString();
            QMessageBox::warning(this, tr("下载失败"),
                                 tr("网络请求错误: %1").arg(reply->errorString()));
        }

        // 执行回调函数，返回下载结果
        if (callback) {
            callback(success);
        }

        // 清理资源
        reply->deleteLater();
        networkManager->deleteLater();
    });
}

//时间列表点击槽函数
void FcMonitorWidget::TimeListWidgetRowChanged(int currentRow)
{
    if (currentRow<0)
    {
        return;
    }
    if (!m_isPaused)
    {
        return;
    }
    // 获取选中项
    QListWidgetItem* selectedItem = timeListWidget->item(currentRow);

    if (!selectedItem) {
        return;
    }
    // 获取存储的UnpackData索引
    int dataIndex = selectedItem->data(Qt::UserRole).toInt();

    if(currentRow > m_currentTimeDataList.size())
    {
        return;
    }

    // 获取对应的UnpackData
    HistoryDataStore::HistoryDataItem data = m_currentTimeDataList[currentRow];

    // 使用现有的TreeView索引快速更新
    for (const auto& pair : data.signalValueMap) {
        QString signalId = QString::fromStdString(pair.first);
        QString signalValue = QString::fromStdString(pair.second);
        updateSignalValueFromTcp(data.srcUnitId,data.msgId, signalId, signalValue);
    }
    //UpdateDataToTree(data);
}

void FcMonitorWidget::sendCurrentSchemeData(SUBSCRIBE_COMMAND cmdMand)
{
    // 这个方法现在已经被SubRuleManager替代
    // 保留用于兼容性，但实际订阅管理由SubRuleManager处理
    qDebug() << "sendCurrentSchemeData called with command" << cmdMand << "- now handled by SubRuleManager";

    if (cmdMand == CMD_SUBSCRIBE && !m_currentSchemeName.isEmpty()) {
        // 如果是订阅命令，通过新的订阅管理系统处理
        updateSubscriptionRules(m_currentSchemeName);
    } else if (cmdMand == CMD_UNSUBSCRIBE) {
        // 如果是取消订阅，清除当前规则
        SubRuleManager::getInstance().updateRules(this, m_currentBenchId, m_lastRules, QList<SubRuleManager::SubscribeRule>());
        m_lastRules.clear();
    }
}

void FcMonitorWidget::on_settingsButton_currentIndexChanged(int index)
{
    int newBenchId = index + 1;

    if (newBenchId != m_currentBenchId) {
        // 清除旧实验台的订阅规则
        if (!m_lastRules.isEmpty()) {
            SubRuleManager::getInstance().delRules(this, m_currentBenchId, m_lastRules);
        }

        // 更新当前实验台号
        m_currentBenchId = newBenchId;

        // 设置TcpClient的当前实验台号
        TcpClient& client = TcpClient::getInstance();
        client.setCurrentBenchId(newBenchId);

        // 重新订阅当前方案到新实验台
        if (!m_currentSchemeName.isEmpty()) {
            updateSubscriptionRules(m_currentSchemeName);
        }

        qDebug() << "Switched to bench" << newBenchId << "for widget" << this;
    }
}

void FcMonitorWidget::onUnitChanged(const QModelIndex &index, const QString &oldUnit, const QString &newUnit,
                                   unsigned int oldUnitID, unsigned int newUnitID)
{
    Q_UNUSED(oldUnit)
    Q_UNUSED(oldUnitID)

    qDebug() << "Unit changed at index" << index << "from" << oldUnit << "to" << newUnit;

    // 获取对应的信号项，重新计算并更新显示值
    QStandardItemModel *model = qobject_cast<QStandardItemModel*>(const_cast<QAbstractItemModel*>(index.model()));
    if (!model) {
        return;
    }

    // 获取同一行的信号值项（第2列，索引为1）
    QStandardItem *valueItem = model->item(index.row(), 1);
    if (!valueItem) {
        return;
    }

    // 获取原始信号值并进行单位转换
    QString currentDisplayValue = valueItem->text();
    bool ok;
    double originalValue = currentDisplayValue.toDouble(&ok);

    if (ok) {
        // 进行单位转换：从旧单位转换回默认单位，再转换到新单位
        UnitStandardItem* unitItem = dynamic_cast<UnitStandardItem*>(model->item(index.row(), 2));
        if (unitItem) {
            unsigned int defaultUnitID = unitItem->defaultUnitID();

            // 先从当前显示单位转换回默认单位
            double defaultValue = UnitModel::unit_change(newUnitID, defaultUnitID, originalValue);

            // 再从默认单位转换到新单位
            double convertedValue = UnitModel::unit_change(defaultUnitID, newUnitID, defaultValue);

            // 针对特殊单位进行格式化处理
            QString newDisplayValue;
            if (newUnit == QString::fromLocal8Bit("时分秒"))
            {
                unsigned int unintSigVal = static_cast<unsigned int>(convertedValue);
                unsigned int unintHour = unintSigVal / 3600;
                unsigned int unintMin = (unintSigVal % 3600) / 60;
                unsigned int unintSec = unintSigVal % 60;
                newDisplayValue = QString("%1:%2:%3")
                                 .arg(unintHour, 2, 10, QChar('0'))
                                 .arg(unintMin, 2, 10, QChar('0'))
                                 .arg(unintSec, 2, 10, QChar('0'));
            }
            else if (newUnit == QString::fromLocal8Bit("度分秒"))
            {
                unsigned int uintDeg = static_cast<unsigned int>(convertedValue);
                unsigned int uintDegM = static_cast<unsigned int>((convertedValue - uintDeg) * 60);
                unsigned int uintDegS = static_cast<unsigned int>((convertedValue - uintDeg - static_cast<double>(uintDegM) / 60) * 3600);
                newDisplayValue = QString("%1 %2 %3")
                                 .arg(uintDeg, 2, 10, QChar('0'))
                                 .arg(uintDegM, 2, 10, QChar('0'))
                                 .arg(uintDegS, 2, 10, QChar('0'));
            }
            else if (newUnit == QString::fromLocal8Bit("度分"))
            {
                unsigned int uintDeg = static_cast<unsigned int>(convertedValue);
                unsigned int uintDegM = static_cast<unsigned int>((convertedValue - uintDeg) * 60);
                newDisplayValue = QString("%1 %2")
                                 .arg(uintDeg, 2, 10, QChar('0'))
                                 .arg(uintDegM, 2, 10, QChar('0'));
            }
            else
            {
                // 普通数值单位
                newDisplayValue = QString::number(convertedValue, 'f', 6);
            }

            // 更新显示值
            valueItem->setText(newDisplayValue);

            qDebug() << "Updated signal value from" << currentDisplayValue << "to" << newDisplayValue;
        }
    }
}


void FcMonitorWidget::onLayoutButtonClicked()
{
    // 切换展开/收缩状态
    m_isExpanded = !m_isExpanded;

    // 更新按钮文案
    ui->layoutButton->setText(m_isExpanded ? "收缩" : "展开");

    // 获取当前活跃的TreeView
    if (m_currentSchemeName.isEmpty() || !m_schemeTreeViews.contains(m_currentSchemeName)) {
        return;
    }

    QTreeView* currentTreeView = m_schemeTreeViews[m_currentSchemeName];
    QStandardItemModel* currentModel = m_schemeModels[m_currentSchemeName];

    if (!currentTreeView || !currentModel) {
        return;
    }

    if (m_isExpanded) {
        // 展开状态：显示所有层级（功能单元消息 -> 主题 -> 信号）
        expandAllLevels(currentTreeView, currentModel);
        qDebug() << "TreeView expanded to show all levels";
    } else {
        // 收缩状态：只显示到功能单元消息层级
        collapseToMessageLevel(currentTreeView, currentModel);
        qDebug() << "TreeView collapsed to message level only";
    }
}

void FcMonitorWidget::expandAllLevels(QTreeView* treeView, QStandardItemModel* model)
{
    if (!treeView || !model) {
        return;
    }

    // 展开所有项目
    treeView->expandAll();
}

void FcMonitorWidget::collapseToMessageLevel(QTreeView* treeView, QStandardItemModel* model)
{
    if (!treeView || !model) {
        return;
    }

    // 首先收缩所有项目
    treeView->collapseAll();

    // 然后只展开第一层级（功能单元消息层级）
    for (int i = 0; i < model->rowCount(); ++i) {
        QModelIndex messageIndex = model->index(i, 0);
        if (messageIndex.isValid()) {
            // 展开功能单元消息节点，但不展开其子节点
            treeView->expand(messageIndex);

            // 确保主题和信号层级保持收缩状态
            QStandardItem* messageItem = model->itemFromIndex(messageIndex);
            if (messageItem) {
                for (int j = 0; j < messageItem->rowCount(); ++j) {
                    QModelIndex topicIndex = messageItem->child(j, 0)->index();
                    if (topicIndex.isValid()) {
                        treeView->collapse(topicIndex);
                    }
                }
            }
        }
    }
}


QList<SubRuleManager::SubscribeRule> FcMonitorWidget::getSchemeSubscribeRules(const QString& schemeName)
{
    QList<SubRuleManager::SubscribeRule> rules;

    FcSchemeData* scheme = m_dataManager->getScheme(schemeName);
    if (!scheme) {
        return rules;
    }

    for (const auto& unitMessage : scheme->unitMessages) {
        SubRuleManager::SubscribeRule rule;
        rule.sourceId = unitMessage.sourceFuncId;
        rule.topicId = unitMessage.pubSubTopicId;
        rules.append(rule);
    }

    return rules;
}

void FcMonitorWidget::updateSubscriptionRules(const QString& schemeName)
{
    // 获取新的订阅规则
    QList<SubRuleManager::SubscribeRule> newRules = getSchemeSubscribeRules(schemeName);

    // 通过SubRuleManager更新订阅规则
    SubRuleManager::getInstance().updateRules(this, m_currentBenchId, m_lastRules, newRules);

    // 更新记录的规则
    m_lastRules = newRules;

    qDebug() << "Updated subscription rules for widget" << this << "scheme:" << schemeName
             << "bench:" << m_currentBenchId << "rules count:" << newRules.size();
}

void FcMonitorWidget::onSplitScreenClicked()
{
    // 创建新的FcMonitorWidget实例
    FcMonitorWidget* newWidget = new FcMonitorWidget();

    // 设置窗口属性
    newWidget->setWindowTitle("FC监控界面 - 分屏");
    newWidget->setWindowFlags(Qt::Window);
    newWidget->setAttribute(Qt::WA_DeleteOnClose);

    // 设置窗口大小和位置
    newWidget->resize(this->size());

    // 计算新窗口位置（在当前窗口右侧移动10Px）
    QPoint currentPos = this->mapToGlobal(QPoint(0, 0));
    QPoint newPos = QPoint(currentPos.x() + 10, currentPos.y());
    newWidget->move(newPos);

    // 显示新窗口
    newWidget->show();

    qDebug() << "Created new split screen FcMonitorWidget at position" << newPos;
}
